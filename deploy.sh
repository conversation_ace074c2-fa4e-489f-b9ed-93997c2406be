#!/bin/bash

# Agentico Deployment Script
# This script sets up and deploys the Agentico AI Agent Platform

set -e  # Exit on any error

echo "🚀 Starting Agentico Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Docker and Docker Compose are installed"
}

# Check if .env file exists
check_env_file() {
    if [ ! -f .env ]; then
        print_warning ".env file not found. Creating from template..."
        cp .env.example .env
        print_warning "Please edit .env file with your configuration before continuing."
        print_warning "Especially set your API keys and change the default secrets!"
        read -p "Press Enter to continue after editing .env file..."
    else
        print_success ".env file found"
    fi
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    # Backend directories
    mkdir -p backend/logs
    mkdir -p backend/uploads
    mkdir -p backend/temp
    
    # Frontend directories
    mkdir -p frontend/.next
    
    print_success "Directories created"
}

# Build and start services
deploy_services() {
    print_status "Building and starting services..."
    
    # Stop any existing containers
    docker-compose down
    
    # Build images
    print_status "Building Docker images..."
    docker-compose build
    
    # Start services
    print_status "Starting services..."
    docker-compose up -d
    
    print_success "Services started"
}

# Wait for services to be healthy
wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    # Wait for database
    print_status "Waiting for PostgreSQL..."
    until docker-compose exec -T postgres pg_isready -U postgres; do
        sleep 2
    done
    
    # Wait for Redis
    print_status "Waiting for Redis..."
    until docker-compose exec -T redis redis-cli ping; do
        sleep 2
    done
    
    # Wait for MinIO
    print_status "Waiting for MinIO..."
    until curl -f http://localhost:9000/minio/health/live &>/dev/null; do
        sleep 2
    done
    
    # Wait for backend
    print_status "Waiting for Backend API..."
    until curl -f http://localhost:8000/health &>/dev/null; do
        sleep 5
    done
    
    print_success "All services are ready!"
}

# Run database migrations
run_migrations() {
    print_status "Running database migrations..."
    
    # Install dependencies and run migrations
    docker-compose exec backend alembic upgrade head
    
    print_success "Database migrations completed"
}

# Create initial data
create_initial_data() {
    print_status "Creating initial data..."
    
    # Create default admin user (optional)
    # docker-compose exec backend python -c "from app.scripts.create_admin import create_admin_user; create_admin_user()"
    
    print_success "Initial data created"
}

# Show deployment status
show_status() {
    echo ""
    echo "🎉 Agentico Deployment Complete!"
    echo ""
    echo "📊 Service Status:"
    docker-compose ps
    echo ""
    echo "🌐 Access URLs:"
    echo "  Frontend:     http://localhost:3000"
    echo "  Backend API:  http://localhost:8000"
    echo "  API Docs:     http://localhost:8000/docs"
    echo "  MinIO Console: http://localhost:9001"
    echo ""
    echo "🔑 Default Credentials:"
    echo "  MinIO: minioadmin / minioadmin"
    echo ""
    echo "📝 Next Steps:"
    echo "  1. Visit http://localhost:3000 to access the platform"
    echo "  2. Create your first user account"
    echo "  3. Set up your AI API keys in the settings"
    echo "  4. Create your first AI agent"
    echo ""
    echo "🔧 Management Commands:"
    echo "  View logs:    docker-compose logs -f [service]"
    echo "  Stop:         docker-compose down"
    echo "  Restart:      docker-compose restart [service]"
    echo "  Update:       git pull && docker-compose build && docker-compose up -d"
    echo ""
}

# Main deployment process
main() {
    echo "🤖 Welcome to Agentico - AI Agent Platform"
    echo "=========================================="
    echo ""
    
    check_docker
    check_env_file
    create_directories
    deploy_services
    wait_for_services
    run_migrations
    create_initial_data
    show_status
}

# Run main function
main "$@"
