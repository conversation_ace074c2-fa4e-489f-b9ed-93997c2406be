"""Super Agent - Monolithic agent with all capabilities"""

from typing import List, Async<PERSON>enerator, Dict, Any, Optional
import asyncio
import time
from datetime import datetime
import json
from enum import Enum
from loguru import logger

from app.agents.base_agent import BaseAgent, AgentMessage, AgentResponse, AgentContext, AgentCapability
from app.agents.llm_agent import LLMAgent
from app.core.config import settings


class TaskIntent(str, Enum):
    """Task intent classification"""
    CODE = "code"
    RESEARCH = "research"
    CREATIVE = "creative"
    ANALYSIS = "analysis"
    AUTOMATION = "automation"
    GENERAL = "general"
    MIXED = "mixed"


class ExecutionMode(str, Enum):
    """Execution mode for super agent"""
    ADAPTIVE = "adaptive"  # Automatically choose best approach
    SEQUENTIAL = "sequential"  # Execute modules in sequence
    PARALLEL = "parallel"  # Execute modules in parallel


class TaskRouter:
    """Intelligent task router for capability selection"""
    
    def __init__(self):
        self.intent_keywords = {
            TaskIntent.CODE: [
                "code", "programming", "debug", "function", "class", "script",
                "python", "javascript", "java", "c++", "html", "css", "sql",
                "algorithm", "compile", "syntax", "error", "bug", "refactor"
            ],
            TaskIntent.RESEARCH: [
                "research", "analyze", "investigate", "study", "explore",
                "find information", "search", "data", "facts", "evidence",
                "compare", "evaluate", "assess", "review", "examine"
            ],
            TaskIntent.CREATIVE: [
                "write", "create", "generate", "compose", "design", "brainstorm",
                "story", "article", "blog", "content", "creative", "imagine",
                "poem", "script", "narrative", "marketing", "copy"
            ],
            TaskIntent.ANALYSIS: [
                "analyze", "calculate", "statistics", "data analysis", "metrics",
                "report", "insights", "trends", "patterns", "visualization",
                "chart", "graph", "dashboard", "summary", "conclusion"
            ],
            TaskIntent.AUTOMATION: [
                "automate", "workflow", "process", "schedule", "batch",
                "pipeline", "integration", "api", "webhook", "trigger",
                "orchestrate", "deploy", "ci/cd", "devops"
            ]
        }
    
    async def analyze_intent(self, message: str) -> TaskIntent:
        """Analyze message to determine task intent"""
        message_lower = message.lower()
        intent_scores = {}
        
        # Score each intent based on keyword matches
        for intent, keywords in self.intent_keywords.items():
            score = sum(1 for keyword in keywords if keyword in message_lower)
            if score > 0:
                intent_scores[intent] = score
        
        if not intent_scores:
            return TaskIntent.GENERAL
        
        # Check for mixed intent (multiple high scores)
        max_score = max(intent_scores.values())
        high_score_intents = [intent for intent, score in intent_scores.items() if score >= max_score * 0.7]
        
        if len(high_score_intents) > 1:
            return TaskIntent.MIXED
        
        return max(intent_scores, key=intent_scores.get)
    
    async def select_capabilities(self, intent: TaskIntent, message: str) -> List[str]:
        """Select required capabilities based on intent"""
        capability_map = {
            TaskIntent.CODE: [
                AgentCapability.CODE_EXECUTION.value,
                AgentCapability.FILE_PROCESSING.value,
                AgentCapability.TEXT_GENERATION.value
            ],
            TaskIntent.RESEARCH: [
                AgentCapability.WEB_BROWSING.value,
                AgentCapability.DATA_ANALYSIS.value,
                AgentCapability.FILE_PROCESSING.value,
                AgentCapability.TEXT_GENERATION.value
            ],
            TaskIntent.CREATIVE: [
                AgentCapability.TEXT_GENERATION.value,
                AgentCapability.IMAGE_GENERATION.value
            ],
            TaskIntent.ANALYSIS: [
                AgentCapability.DATA_ANALYSIS.value,
                AgentCapability.FILE_PROCESSING.value,
                AgentCapability.TEXT_GENERATION.value
            ],
            TaskIntent.AUTOMATION: [
                AgentCapability.WORKFLOW_AUTOMATION.value,
                AgentCapability.API_INTEGRATION.value,
                AgentCapability.CODE_EXECUTION.value
            ],
            TaskIntent.GENERAL: [
                AgentCapability.TEXT_GENERATION.value
            ],
            TaskIntent.MIXED: [
                AgentCapability.TEXT_GENERATION.value,
                AgentCapability.CODE_EXECUTION.value,
                AgentCapability.DATA_ANALYSIS.value,
                AgentCapability.FILE_PROCESSING.value
            ]
        }
        
        return capability_map.get(intent, [AgentCapability.TEXT_GENERATION.value])


class ContextManager:
    """Manages context across different capabilities"""
    
    def __init__(self):
        self.conversation_context = {}
        self.file_context = {}
        self.code_context = {}
        self.research_context = {}
        self.analysis_context = {}
    
    async def update_context(self, capability: str, data: Dict[str, Any]):
        """Update context based on capability output"""
        if capability == AgentCapability.CODE_EXECUTION.value:
            self.code_context.update(data)
        elif capability == AgentCapability.WEB_BROWSING.value:
            self.research_context.update(data)
        elif capability == AgentCapability.DATA_ANALYSIS.value:
            self.analysis_context.update(data)
        elif capability == AgentCapability.FILE_PROCESSING.value:
            self.file_context.update(data)
    
    def get_relevant_context(self, capabilities: List[str]) -> Dict[str, Any]:
        """Get relevant context for given capabilities"""
        context = {}
        
        for capability in capabilities:
            if capability == AgentCapability.CODE_EXECUTION.value:
                context.update(self.code_context)
            elif capability == AgentCapability.WEB_BROWSING.value:
                context.update(self.research_context)
            elif capability == AgentCapability.DATA_ANALYSIS.value:
                context.update(self.analysis_context)
            elif capability == AgentCapability.FILE_PROCESSING.value:
                context.update(self.file_context)
        
        return context


class SuperAgent(BaseAgent):
    """Monolithic super agent with all capabilities"""
    
    def __init__(self, agent):
        super().__init__(agent)
        
        # Initialize all capabilities
        self.all_capabilities = [
            AgentCapability.TEXT_GENERATION.value,
            AgentCapability.CODE_EXECUTION.value,
            AgentCapability.WEB_BROWSING.value,
            AgentCapability.FILE_PROCESSING.value,
            AgentCapability.IMAGE_GENERATION.value,
            AgentCapability.DATA_ANALYSIS.value,
            AgentCapability.API_INTEGRATION.value,
            AgentCapability.WORKFLOW_AUTOMATION.value
        ]
        
        # Override capabilities with all available
        self.capabilities = self.all_capabilities
        
        # Initialize components
        self.task_router = TaskRouter()
        self.context_manager = ContextManager()
        self.llm_agent = LLMAgent(agent)  # Use LLM agent as base processor
        
        # Configuration
        self.execution_mode = ExecutionMode(agent.config.get('execution_mode', 'adaptive'))
        self.capability_weights = agent.config.get('capability_weights', {})
        
        logger.info(f"SuperAgent initialized with {len(self.all_capabilities)} capabilities")
    
    async def validate_input(self, messages: List[AgentMessage]) -> bool:
        """Validate input messages"""
        if not messages:
            return False
        
        # Check if last message is from user
        if messages[-1].role not in ["user", "system"]:
            return False
        
        return True
    
    async def execute(
        self,
        messages: List[AgentMessage],
        context: AgentContext
    ) -> AsyncGenerator[AgentResponse, None]:
        """Execute super agent with intelligent capability selection"""
        start_time = time.time()
        
        try:
            # Validate input
            if not await self.validate_input(messages):
                yield AgentResponse(
                    content="Invalid input messages",
                    success=False,
                    error="Invalid input messages"
                )
                return
            
            # Get the latest user message
            user_message = messages[-1].content
            
            # Analyze task intent
            intent = await self.task_router.analyze_intent(user_message)
            logger.info(f"Task intent detected: {intent}")
            
            # Select required capabilities
            required_capabilities = await self.task_router.select_capabilities(intent, user_message)
            logger.info(f"Selected capabilities: {required_capabilities}")
            
            # Get relevant context
            relevant_context = self.context_manager.get_relevant_context(required_capabilities)
            
            # Enhance system prompt based on intent and capabilities
            enhanced_prompt = await self.build_enhanced_prompt(intent, required_capabilities, relevant_context)
            
            # Create enhanced messages with system prompt
            enhanced_messages = messages.copy()
            if enhanced_prompt:
                system_message = AgentMessage(
                    role="system",
                    content=enhanced_prompt,
                    timestamp=datetime.utcnow()
                )
                enhanced_messages.insert(0, system_message)
            
            # Execute using LLM agent with enhanced capabilities
            async for response in self.llm_agent.execute(enhanced_messages, context):
                # Update context with response
                if response.success and response.metadata:
                    for capability in required_capabilities:
                        await self.context_manager.update_context(capability, response.metadata)
                
                # Enhance response metadata
                if response.metadata is None:
                    response.metadata = {}
                
                response.metadata.update({
                    'intent': intent.value,
                    'capabilities_used': required_capabilities,
                    'execution_mode': self.execution_mode.value,
                    'execution_time': time.time() - start_time
                })
                
                yield response
            
            logger.info(f"SuperAgent execution completed in {time.time() - start_time:.2f}s")
            
        except Exception as e:
            logger.error(f"SuperAgent execution error: {str(e)}")
            yield AgentResponse(
                content=f"An error occurred during execution: {str(e)}",
                success=False,
                error=str(e),
                metadata={
                    'intent': intent.value if 'intent' in locals() else 'unknown',
                    'execution_time': time.time() - start_time
                }
            )
    
    async def build_enhanced_prompt(self, intent: TaskIntent, capabilities: List[str], context: Dict[str, Any]) -> str:
        """Build enhanced system prompt based on intent and capabilities"""
        base_prompt = self.agent.system_prompt or "You are a helpful AI assistant."
        
        # Intent-specific instructions
        intent_instructions = {
            TaskIntent.CODE: """
You are now operating in CODE mode with the following capabilities:
- Code generation and analysis
- Debugging and error fixing
- File processing and manipulation
- Best practices and optimization suggestions

When handling code tasks:
- Provide clean, well-documented code
- Include error handling where appropriate
- Suggest improvements and alternatives
- Use proper syntax highlighting in responses""",
            
            TaskIntent.RESEARCH: """
You are now operating in RESEARCH mode with the following capabilities:
- Web browsing and information gathering
- Data analysis and synthesis
- Fact-checking and verification
- Comprehensive reporting

When conducting research:
- Verify information from multiple sources
- Provide clear citations and references
- Present balanced perspectives
- Structure information logically""",
            
            TaskIntent.CREATIVE: """
You are now operating in CREATIVE mode with the following capabilities:
- Creative writing and content generation
- Brainstorming and ideation
- Image generation concepts
- Marketing and copywriting

When creating content:
- Be imaginative and original
- Consider target audience
- Maintain consistent tone and style
- Provide multiple creative options when appropriate""",
            
            TaskIntent.ANALYSIS: """
You are now operating in ANALYSIS mode with the following capabilities:
- Data analysis and interpretation
- Statistical calculations
- Trend identification
- Report generation

When analyzing data:
- Provide clear insights and conclusions
- Use appropriate statistical methods
- Visualize data when helpful
- Explain methodology and assumptions""",
            
            TaskIntent.AUTOMATION: """
You are now operating in AUTOMATION mode with the following capabilities:
- Workflow automation design
- API integration
- Process optimization
- System orchestration

When designing automation:
- Consider error handling and edge cases
- Provide scalable solutions
- Include monitoring and logging
- Document processes clearly""",
            
            TaskIntent.MIXED: """
You are now operating in MIXED mode with multiple capabilities:
- Adapt your approach based on the specific requirements
- Combine different capabilities as needed
- Provide comprehensive solutions
- Explain your reasoning for capability selection"""
        }
        
        # Get intent-specific instructions
        intent_prompt = intent_instructions.get(intent, "")
        
        # Add capability information
        capability_info = f"\n\nActive capabilities: {', '.join(capabilities)}"
        
        # Add context information if available
        context_info = ""
        if context:
            context_info = f"\n\nRelevant context from previous interactions:\n{json.dumps(context, indent=2)}"
        
        return f"{base_prompt}\n\n{intent_prompt}{capability_info}{context_info}"
    
    def get_system_prompt(self) -> str:
        """Get system prompt for super agent"""
        return self.agent.system_prompt or "You are a powerful AI assistant with multiple specialized capabilities."