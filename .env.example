# Agentico Environment Configuration
# Copy this file to .env and update the values

# AI API Keys (Optional - for LLM functionality)
OPENAI_API_KEY=your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here
OPENROUTER_API_KEY=your-openrouter-api-key-here

# Security Keys (CHANGE THESE IN PRODUCTION!)
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
NEXTAUTH_SECRET=your-super-secret-nextauth-key-change-in-production

# Database Configuration
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/agentico
POSTGRES_DB=agentico
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres

# Redis Configuration
REDIS_URL=redis://localhost:6379

# File Storage Configuration
FILE_STORAGE_TYPE=local
FILE_STORAGE_PATH=./storage
FILE_UPLOAD_PATH=./uploads
MAX_FILE_SIZE_MB=100

# Application URLs
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
NEXTAUTH_URL=http://localhost:3000

# Environment
ENVIRONMENT=development

# Agent Configuration
DEFAULT_LLM_MODEL=gpt-3.5-turbo
AGENT_TIMEOUT_SECONDS=300
MAX_AGENT_MEMORY_MB=512
MAX_AGENT_CPU_PERCENT=50

# File Upload Configuration
MAX_FILE_SIZE_MB=100
ALLOWED_FILE_TYPES=txt,pdf,doc,docx,jpg,jpeg,png,gif,csv,json,xml

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379
CELERY_RESULT_BACKEND=redis://localhost:6379

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json
