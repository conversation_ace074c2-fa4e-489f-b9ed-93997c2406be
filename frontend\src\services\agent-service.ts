import { apiClient } from '@/lib/api-client';
import type { Agent, AgentCreate, AgentUpdate, AgentExecuteRequest } from '@/types/agent';

class AgentService {
  async getAgents(params?: {
    skip?: number;
    limit?: number;
    agent_type?: string;
    is_active?: boolean;
  }): Promise<Agent[]> {
    const response = await apiClient.get('/agents', { params });
    return response.data;
  }

  async getPublicAgents(params?: {
    skip?: number;
    limit?: number;
    agent_type?: string;
  }): Promise<Agent[]> {
    const response = await apiClient.get('/agents/public', { params });
    return response.data;
  }

  async getAgentTypes(): Promise<{ types: Array<{ value: string; name: string; description: string }> }> {
    const response = await apiClient.get('/agents/types');
    return response.data;
  }

  async getAgent(id: number): Promise<Agent> {
    const response = await apiClient.get(`/agents/${id}`);
    return response.data;
  }

  async createAgent(data: AgentCreate): Promise<Agent> {
    const response = await apiClient.post('/agents', data);
    return response.data;
  }

  async updateAgent(id: number, data: AgentUpdate): Promise<Agent> {
    const response = await apiClient.put(`/agents/${id}`, data);
    return response.data;
  }

  async deleteAgent(id: number): Promise<void> {
    await apiClient.delete(`/agents/${id}`);
  }

  async getAgentStats(id: number): Promise<{
    agent: Agent;
    stats: {
      tasks_count: number;
      conversations_count: number;
      success_rate: number;
      total_executions: number;
      average_execution_time: string;
      uptime_days: number;
    };
  }> {
    const response = await apiClient.get(`/agents/${id}/stats`);
    return response.data;
  }

  async executeAgent(id: number, data: AgentExecuteRequest): Promise<{
    message: string;
    task_id: string;
    agent_id: number;
  }> {
    const response = await apiClient.post(`/agents/${id}/execute`, data);
    return response.data;
  }

  async searchAgents(query: string, params?: {
    skip?: number;
    limit?: number;
  }): Promise<Agent[]> {
    const response = await apiClient.get('/agents/search', {
      params: { q: query, ...params }
    });
    return response.data;
  }
}

export const agentService = new AgentService();
