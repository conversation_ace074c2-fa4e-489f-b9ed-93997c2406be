"""
Agent execution tasks
"""

from celery import current_task
from app.core.celery import celery_app
from app.core.database import AsyncSessionLocal
from app.services.agent_service import AgentService
from app.services.task_service import TaskService
from app.services.conversation_service import ConversationService
from app.core.socketio_manager import send_to_user
from app.agents.agent_factory import execution_engine
from app.agents.base_agent import AgentMessage, AgentContext
from loguru import logger
import asyncio
import time
from datetime import datetime


@celery_app.task(bind=True)
def execute_agent_task(self, agent_id: int, messages: list, context: dict):
    """Execute an agent with messages"""

    async def _execute():
        async with AsyncSessionLocal() as db:
            agent_service = AgentService(db)

            try:
                # Get agent
                agent = await agent_service.get_by_id(agent_id)
                if not agent:
                    raise Exception("Agent not found")

                # Update agent status
                await agent_service.update_status(agent_id, "running")

                # Send real-time update
                await send_to_user(context["user_id"], "agent_started", {
                    "agent_id": agent_id,
                    "status": "running"
                })

                start_time = time.time()

                # Execute agent
                responses = []
                async for response in execution_engine.execute_agent(agent, messages, context):
                    responses.append(response)

                    # Send real-time response
                    await send_to_user(context["user_id"], "agent_response", {
                        "agent_id": agent_id,
                        "response": response.__dict__,
                        "conversation_id": context.get("conversation_id")
                    })

                execution_time = time.time() - start_time

                # Update agent statistics
                success = all(r.success for r in responses)
                await agent_service.increment_execution_stats(agent_id, success, execution_time)

                # Reset agent status
                await agent_service.reset_status(agent_id)

                # Send completion notification
                await send_to_user(context["user_id"], "agent_completed", {
                    "agent_id": agent_id,
                    "status": "completed",
                    "execution_time": execution_time,
                    "success": success
                })

                return {
                    "success": success,
                    "responses": [r.__dict__ for r in responses],
                    "execution_time": execution_time
                }

            except Exception as e:
                logger.error(f"Agent execution failed: {e}")

                # Reset agent status
                await agent_service.reset_status(agent_id)

                # Update agent statistics
                execution_time = time.time() - start_time if 'start_time' in locals() else 0
                await agent_service.increment_execution_stats(agent_id, False, execution_time)

                # Send failure notification
                await send_to_user(context["user_id"], "agent_failed", {
                    "agent_id": agent_id,
                    "status": "failed",
                    "error": str(e)
                })

                raise

    # Run the async function
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(_execute())
    finally:
        loop.close()


@celery_app.task(bind=True)
def execute_agent_conversation_task(self, conversation_id: int, agent_id: int, user_id: int):
    """Execute agent in conversation context"""

    async def _execute():
        async with AsyncSessionLocal() as db:
            conversation_service = ConversationService(db)
            agent_service = AgentService(db)

            try:
                # Get conversation and recent messages
                conversation = await conversation_service.get_by_id(conversation_id)
                if not conversation:
                    raise Exception("Conversation not found")

                # Get agent
                agent = await agent_service.get_by_id(agent_id)
                if not agent:
                    raise Exception("Agent not found")

                # Get recent messages for context
                recent_messages = await conversation_service.get_recent_messages(
                    conversation_id, limit=20
                )

                # Convert to agent messages
                agent_messages = [
                    AgentMessage(
                        role=msg.role.value,
                        content=msg.content,
                        timestamp=msg.created_at,
                        metadata=msg.metadata
                    )
                    for msg in recent_messages
                ]

                # Create execution context
                context = AgentContext(
                    agent_id=agent_id,
                    user_id=user_id,
                    conversation_id=conversation_id
                )

                # Update agent status
                await agent_service.update_status(agent_id, "running")

                # Send typing indicator
                await send_to_user(user_id, "agent_typing", {
                    "conversation_id": conversation_id,
                    "agent_id": agent_id
                })

                start_time = time.time()

                # Execute agent
                full_response = ""
                async for response in execution_engine.execute_agent(agent, agent_messages, context.__dict__):
                    if response.success:
                        full_response += response.content

                        # Send streaming response
                        await send_to_user(user_id, "agent_response_stream", {
                            "conversation_id": conversation_id,
                            "content": response.content,
                            "streaming": response.metadata.get("streaming", False)
                        })

                # Add agent response to conversation
                if full_response:
                    await conversation_service.add_message(
                        conversation_id=conversation_id,
                        role="assistant",
                        content=full_response,
                        metadata={"agent_id": agent_id}
                    )

                execution_time = time.time() - start_time

                # Update agent statistics
                await agent_service.increment_execution_stats(agent_id, True, execution_time)

                # Reset agent status
                await agent_service.reset_status(agent_id)

                # Send completion notification
                await send_to_user(user_id, "agent_response_complete", {
                    "conversation_id": conversation_id,
                    "agent_id": agent_id,
                    "message_content": full_response
                })

                return {
                    "success": True,
                    "response": full_response,
                    "execution_time": execution_time
                }

            except Exception as e:
                logger.error(f"Agent conversation execution failed: {e}")

                # Reset agent status
                await agent_service.reset_status(agent_id)

                # Update agent statistics
                execution_time = time.time() - start_time if 'start_time' in locals() else 0
                await agent_service.increment_execution_stats(agent_id, False, execution_time)

                # Send error message to conversation
                await conversation_service.add_message(
                    conversation_id=conversation_id,
                    role="assistant",
                    content="I'm sorry, I encountered an error while processing your message. Please try again.",
                    metadata={"agent_id": agent_id, "error": str(e)}
                )

                # Send failure notification
                await send_to_user(user_id, "agent_error", {
                    "conversation_id": conversation_id,
                    "agent_id": agent_id,
                    "error": str(e)
                })

                raise

    # Run the async function
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(_execute())
    finally:
        loop.close()


@celery_app.task
def cleanup_agent_resources(agent_id: int):
    """Clean up agent resources after execution"""

    async def _cleanup():
        async with AsyncSessionLocal() as db:
            agent_service = AgentService(db)

            try:
                # Perform cleanup operations
                # - Stop any running containers
                # - Clean up temporary files
                # - Reset agent status

                await agent_service.reset_status(agent_id)
                logger.info(f"Cleaned up resources for agent {agent_id}")

            except Exception as e:
                logger.error(f"Failed to cleanup agent {agent_id}: {e}")

    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        loop.run_until_complete(_cleanup())
    finally:
        loop.close()
