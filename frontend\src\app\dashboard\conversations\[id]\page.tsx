'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import {
  ArrowLeftIcon,
  PaperAirplaneIcon,
  CpuChipIcon,
  UserIcon,
  EllipsisVerticalIcon,
  TrashIcon,
  ArchiveBoxIcon,
} from '@heroicons/react/24/outline';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { conversationService } from '@/services/conversation-service';
import { agentService } from '@/services/agent-service';
import type { Conversation, Message } from '@/types/conversation';
import type { Agent } from '@/types/agent';

export default function ConversationPage() {
  const params = useParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const conversationId = parseInt(params.id as string);
  const [newMessage, setNewMessage] = useState('');
  const [selectedAgent, setSelectedAgent] = useState<number | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Fetch conversation details
  const { data: conversation, isLoading: conversationLoading } = useQuery({
    queryKey: ['conversation', conversationId],
    queryFn: () => conversationService.getConversation(conversationId),
    enabled: !!conversationId,
  });

  // Fetch conversation messages
  const { data: messages = [], isLoading: messagesLoading } = useQuery({
    queryKey: ['conversation-messages', conversationId],
    queryFn: () => conversationService.getMessages(conversationId),
    enabled: !!conversationId,
  });

  // Fetch available agents
  const { data: agents } = useQuery({
    queryKey: ['agents'],
    queryFn: () => agentService.getAgents(),
  });

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: (messageData: { content: string; agent_id?: number }) =>
      conversationService.sendMessage(conversationId, messageData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['conversation-messages', conversationId] });
      queryClient.invalidateQueries({ queryKey: ['conversation', conversationId] });
      setNewMessage('');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to send message');
    },
  });

  // Delete conversation mutation
  const deleteConversationMutation = useMutation({
    mutationFn: () => conversationService.deleteConversation(conversationId),
    onSuccess: () => {
      toast.success('Conversation deleted successfully');
      router.push('/dashboard/conversations');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete conversation');
    },
  });

  // Archive conversation mutation
  const archiveConversationMutation = useMutation({
    mutationFn: () => conversationService.archiveConversation(conversationId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['conversation', conversationId] });
      toast.success('Conversation archived successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to archive conversation');
    },
  });

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Set selected agent from conversation if available
  useEffect(() => {
    if (conversation?.agent_id && !selectedAgent) {
      setSelectedAgent(conversation.agent_id);
    }
  }, [conversation, selectedAgent]);

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newMessage.trim()) {
      toast.error('Please enter a message');
      return;
    }

    sendMessageMutation.mutate({
      content: newMessage,
      agent_id: selectedAgent || undefined,
    });
  };

  const handleDeleteConversation = () => {
    if (confirm('Are you sure you want to delete this conversation? This action cannot be undone.')) {
      deleteConversationMutation.mutate();
    }
  };

  const formatMessageTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getSelectedAgentName = () => {
    if (!selectedAgent) return 'No agent selected';
    const agent = agents?.find((a: Agent) => a.id === selectedAgent);
    return agent?.name || 'Unknown agent';
  };

  if (conversationLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!conversation) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">Conversation not found</p>
        <Button
          onClick={() => router.push('/dashboard/conversations')}
          className="mt-4"
        >
          Back to Conversations
        </Button>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-[calc(100vh-2rem)]">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeftIcon className="h-4 w-4" />
            Back
          </Button>
          <div>
            <h1 className="text-xl font-semibold text-gray-900">{conversation.title}</h1>
            {conversation.description && (
              <p className="text-sm text-gray-500">{conversation.description}</p>
            )}
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Agent Selection */}
          <select
            value={selectedAgent || ''}
            onChange={(e) => setSelectedAgent(e.target.value ? parseInt(e.target.value) : null)}
            className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">No agent</option>
            {agents?.map((agent: Agent) => (
              <option key={agent.id} value={agent.id}>
                {agent.name}
              </option>
            ))}
          </select>
          
          {/* Actions Menu */}
          <div className="relative">
            <Button
              variant="outline"
              size="sm"
              onClick={() => archiveConversationMutation.mutate()}
              disabled={archiveConversationMutation.isPending}
              className="flex items-center gap-2"
            >
              <ArchiveBoxIcon className="h-4 w-4" />
              Archive
            </Button>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleDeleteConversation}
            disabled={deleteConversationMutation.isPending}
            className="flex items-center gap-2 text-red-600 hover:text-red-700"
          >
            <TrashIcon className="h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messagesLoading ? (
          <div className="flex items-center justify-center h-32">
            <LoadingSpinner size="sm" />
          </div>
        ) : messages.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <CpuChipIcon className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Start the conversation</h3>
            <p className="text-gray-500">
              {selectedAgent ? `Send a message to ${getSelectedAgentName()}` : 'Select an agent and send a message to get started'}
            </p>
          </div>
        ) : (
          messages.map((message: Message) => (
            <div
              key={message.id}
              className={`flex gap-3 ${
                message.role === 'user' ? 'justify-end' : 'justify-start'
              }`}
            >
              {message.role !== 'user' && (
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <CpuChipIcon className="h-5 w-5 text-blue-600" />
                  </div>
                </div>
              )}
              
              <div
                className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                  message.role === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-900'
                }`}
              >
                <p className="text-sm">{message.content}</p>
                <p
                  className={`text-xs mt-1 ${
                    message.role === 'user' ? 'text-blue-100' : 'text-gray-500'
                  }`}
                >
                  {formatMessageTime(message.created_at)}
                </p>
              </div>
              
              {message.role === 'user' && (
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <UserIcon className="h-5 w-5 text-gray-600" />
                  </div>
                </div>
              )}
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="border-t border-gray-200 p-4 bg-white">
        <form onSubmit={handleSendMessage} className="flex gap-2">
          <Input
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder={selectedAgent ? `Message ${getSelectedAgentName()}...` : 'Type a message...'}
            disabled={sendMessageMutation.isPending}
            className="flex-1"
          />
          <Button
            type="submit"
            disabled={sendMessageMutation.isPending || !newMessage.trim()}
            className="flex items-center gap-2"
          >
            {sendMessageMutation.isPending ? (
              <LoadingSpinner size="sm" />
            ) : (
              <PaperAirplaneIcon className="h-4 w-4" />
            )}
            Send
          </Button>
        </form>
        
        {!selectedAgent && (
          <p className="text-xs text-gray-500 mt-2">
            Select an agent above to start chatting
          </p>
        )}
      </div>
    </div>
  );
}