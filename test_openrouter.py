import litellm
import os
import asyncio

async def test_openrouter():
    try:
        print("Testing OpenRouter with LiteLLM...")
        print(f"OPENROUTER_API_KEY set: {bool(os.environ.get('OPENROUTER_API_KEY'))}")
        
        response = await litellm.acompletion(
            model='openrouter/anthropic/claude-3.5-sonnet',
            messages=[{'role': 'user', 'content': 'Hello, just say hi back'}],
            max_tokens=10
        )
        print('Success:', response.choices[0].message.content)
        
    except Exception as e:
        print('Error:', str(e))
        print('Error type:', type(e))
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_openrouter())