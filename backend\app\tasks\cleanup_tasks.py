"""
Cleanup and maintenance tasks
"""

from app.core.celery import celery_app
from app.core.database import AsyncSessionLocal, check_database_health
from app.services.conversation_service import ConversationService
from app.services.task_service import TaskService
from loguru import logger
import asyncio
import redis
from datetime import datetime, timedelta
from app.core.config import settings


@celery_app.task
def cleanup_expired_tasks():
    """Clean up expired and old tasks"""
    
    async def _cleanup():
        async with AsyncSessionLocal() as db:
            task_service = TaskService(db)
            
            try:
                # Get tasks older than 7 days
                cutoff_date = datetime.utcnow() - timedelta(days=7)
                
                # In a real implementation, you would add a method to get old tasks
                # For now, just log the cleanup
                logger.info("Cleaning up expired tasks")
                
                # Could also clean up:
                # - Failed tasks older than X days
                # - Completed tasks older than X days (keep only recent ones)
                # - Orphaned task records
                
                return {"success": True, "cleaned_up": 0}
                
            except Exception as e:
                logger.error(f"Task cleanup failed: {e}")
                raise
    
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(_cleanup())
    finally:
        loop.close()


@celery_app.task
def cleanup_old_files():
    """Clean up old temporary files"""
    
    async def _cleanup():
        try:
            # Import here to avoid circular imports
            from app.tasks.file_tasks import cleanup_temporary_files
            
            # Run the file cleanup task
            result = cleanup_temporary_files.delay()
            
            logger.info("Initiated file cleanup task")
            
            return {"success": True, "task_id": result.id}
            
        except Exception as e:
            logger.error(f"File cleanup initiation failed: {e}")
            raise
    
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(_cleanup())
    finally:
        loop.close()


@celery_app.task
def cleanup_old_conversations():
    """Archive old conversations"""
    
    async def _cleanup():
        async with AsyncSessionLocal() as db:
            conversation_service = ConversationService(db)
            
            try:
                # Archive conversations older than 30 days
                archived_count = await conversation_service.archive_old_conversations(days=30)
                
                logger.info(f"Archived {archived_count} old conversations")
                
                return {"success": True, "archived_count": archived_count}
                
            except Exception as e:
                logger.error(f"Conversation cleanup failed: {e}")
                raise
    
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(_cleanup())
    finally:
        loop.close()


@celery_app.task
def health_check():
    """Perform system health check"""
    
    async def _check():
        health_status = {
            "timestamp": datetime.utcnow().isoformat(),
            "services": {}
        }
        
        try:
            # Check database
            db_healthy = await check_database_health()
            health_status["services"]["database"] = {
                "status": "healthy" if db_healthy else "unhealthy",
                "type": "postgresql"
            }
            
            # Check Redis
            try:
                r = redis.from_url(settings.REDIS_URL)
                r.ping()
                health_status["services"]["redis"] = {
                    "status": "healthy",
                    "type": "redis"
                }
            except Exception as e:
                health_status["services"]["redis"] = {
                    "status": "unhealthy",
                    "error": str(e),
                    "type": "redis"
                }
            
            # Check Celery workers
            try:
                from app.core.celery import celery_app
                inspect = celery_app.control.inspect()
                active_workers = inspect.active()
                
                health_status["services"]["celery"] = {
                    "status": "healthy" if active_workers else "unhealthy",
                    "active_workers": len(active_workers) if active_workers else 0,
                    "type": "celery"
                }
            except Exception as e:
                health_status["services"]["celery"] = {
                    "status": "unhealthy",
                    "error": str(e),
                    "type": "celery"
                }
            
            # Overall status
            all_healthy = all(
                service["status"] == "healthy" 
                for service in health_status["services"].values()
            )
            health_status["overall_status"] = "healthy" if all_healthy else "degraded"
            
            logger.info(f"Health check completed: {health_status['overall_status']}")
            
            return health_status
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            health_status["overall_status"] = "unhealthy"
            health_status["error"] = str(e)
            return health_status
    
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(_check())
    finally:
        loop.close()


@celery_app.task
def cleanup_websocket_connections():
    """Clean up stale WebSocket connections"""
    
    async def _cleanup():
        try:
            from app.core.socketio_manager import active_connections
            
            # In a real implementation, you would:
            # 1. Check for stale connections
            # 2. Remove inactive sessions
            # 3. Clean up connection tracking
            
            current_connections = len(active_connections)
            logger.info(f"Current WebSocket connections: {current_connections}")
            
            return {"success": True, "active_connections": current_connections}
            
        except Exception as e:
            logger.error(f"WebSocket cleanup failed: {e}")
            raise
    
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(_cleanup())
    finally:
        loop.close()


@celery_app.task
def generate_system_metrics():
    """Generate system performance metrics"""
    
    async def _generate():
        try:
            metrics = {
                "timestamp": datetime.utcnow().isoformat(),
                "system": {
                    "uptime": "unknown",  # Would calculate actual uptime
                    "memory_usage": "unknown",  # Would get actual memory usage
                    "cpu_usage": "unknown",  # Would get actual CPU usage
                },
                "application": {
                    "active_agents": 0,  # Would count from database
                    "active_conversations": 0,  # Would count from database
                    "pending_tasks": 0,  # Would count from Celery
                    "websocket_connections": 0,  # Would count active connections
                }
            }
            
            # In a real implementation, you would:
            # 1. Collect actual system metrics
            # 2. Query database for application metrics
            # 3. Store metrics in time-series database
            # 4. Generate alerts if thresholds are exceeded
            
            logger.info("Generated system metrics")
            
            return metrics
            
        except Exception as e:
            logger.error(f"Metrics generation failed: {e}")
            raise
    
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(_generate())
    finally:
        loop.close()
