export interface Agent {
  id: number;
  name: string;
  description?: string;
  agent_type: string;
  model: string;
  system_prompt?: string;
  temperature: string;
  max_tokens: number;
  status: string;
  is_active: boolean;
  is_public: boolean;
  capabilities?: string[];
  tools?: Array<{
    name: string;
    description: string;
    parameters?: Record<string, any>;
  }>;
  docker_image?: string;
  config?: Record<string, any>;
  environment_vars?: Record<string, any>;
  max_execution_time: number;
  max_memory_mb: number;
  max_cpu_percent: number;
  total_executions: number;
  successful_executions: number;
  failed_executions: number;
  success_rate: number;
  average_execution_time?: string;
  created_at: string;
  updated_at: string;
  last_executed_at?: string;
  owner_id: number;
}

export interface AgentCreate {
  name: string;
  description?: string;
  agent_type: string;
  model?: string;
  system_prompt?: string;
  temperature?: string;
  max_tokens?: number;
  capabilities?: string[];
  tools?: Array<{
    name: string;
    description: string;
    parameters?: Record<string, any>;
  }>;
  config?: Record<string, any>;
  environment_vars?: Record<string, any>;
  is_public?: boolean;
  max_execution_time?: number;
  max_memory_mb?: number;
  max_cpu_percent?: number;
}

export interface AgentUpdate {
  name?: string;
  description?: string;
  system_prompt?: string;
  temperature?: string;
  max_tokens?: number;
  capabilities?: string[];
  tools?: Array<{
    name: string;
    description: string;
    parameters?: Record<string, any>;
  }>;
  config?: Record<string, any>;
  environment_vars?: Record<string, any>;
  is_public?: boolean;
  is_active?: boolean;
  max_execution_time?: number;
  max_memory_mb?: number;
  max_cpu_percent?: number;
}

export interface AgentExecuteRequest {
  messages: Array<{
    role: string;
    content: string;
    timestamp?: string;
    metadata?: Record<string, any>;
  }>;
  context?: {
    conversation_id?: number;
    task_id?: number;
    files?: Array<Record<string, any>>;
    variables?: Record<string, any>;
  };
}

export interface AgentResponse {
  content: string;
  success: boolean;
  metadata?: Record<string, any>;
  tool_calls?: Array<Record<string, any>>;
  error?: string;
}
