"""
File management endpoints
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File as FastAPIFile
from fastapi.responses import FileResponse, StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel
import os
from pathlib import Path

from app.core.database import get_async_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.file import File, FileStatus, FileType
from app.services.file_service import FileService
from app.services.local_storage_service import local_storage
from app.core.config import settings
from app.core.exceptions import NotFoundError
from app.tasks.file_tasks import process_uploaded_file

router = APIRouter()


class FileUploadResponse(BaseModel):
    id: int
    filename: str
    file_size: int
    content_type: str
    status: str
    public_url: str
    message: str


@router.post("/upload", response_model=FileUploadResponse)
async def upload_file(
    file: UploadFile = FastAPIFile(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Upload a file"""
    file_service = FileService(db)
    
    # Validate file size
    if file.size and file.size > settings.MAX_FILE_SIZE_MB * 1024 * 1024:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File size exceeds maximum allowed size of {settings.MAX_FILE_SIZE_MB}MB"
        )
    
    # Validate file type
    file_ext = Path(file.filename).suffix.lower().lstrip('.')
    if file_ext not in settings.ALLOWED_FILE_TYPES:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File type '{file_ext}' not allowed. Allowed types: {', '.join(settings.ALLOWED_FILE_TYPES)}"
        )
    
    try:
        # Upload to local storage
        upload_result = local_storage.upload_file(
            file_data=file.file,
            filename=file.filename,
            user_id=current_user.id,
            content_type=file.content_type
        )
        
        # Create file record in database
        file_data = {
            "filename": file.filename,
            "original_filename": file.filename,
            "file_size": upload_result["file_size"],
            "file_size_mb": round(upload_result["file_size"] / (1024 * 1024), 2),
            "mime_type": upload_result["content_type"],
            "file_extension": file_ext,
            "file_hash": upload_result["file_hash"],
            "storage_path": upload_result["file_path"],
            "public_url": upload_result["public_url"],
            "status": FileStatus.UPLOADED,
            "file_type": determine_file_type(file_ext),
            "owner_id": current_user.id,
            "metadata": {
                "storage_type": "local",
                "upload_ip": "127.0.0.1",  # In production, get real IP
                "user_agent": "Agentico"
            }
        }
        
        file_obj = await file_service.create(file_data)
        
        # Start background processing
        process_uploaded_file.delay(file_obj.id, current_user.id)
        
        return FileUploadResponse(
            id=file_obj.id,
            filename=file_obj.filename,
            file_size=file_obj.file_size,
            content_type=file_obj.mime_type,
            status=file_obj.status.value,
            public_url=file_obj.public_url,
            message="File uploaded successfully and processing started"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload file: {str(e)}"
        )


@router.get("/download/{file_path:path}")
async def download_file(
    file_path: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Download a file"""
    file_service = FileService(db)
    
    try:
        # Find file by storage path
        files = await file_service.search_files(
            search_term="",  # We'll filter by path
            owner_id=current_user.id
        )
        
        file_obj = None
        for f in files:
            if f.storage_path and file_path in f.storage_path:
                file_obj = f
                break
        
        if not file_obj:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
        
        # Check permissions
        if file_obj.owner_id != current_user.id and not file_obj.is_public:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Get file from storage
        file_data = local_storage.download_file(file_path)
        if not file_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found in storage"
            )
        
        # Increment download count
        await file_service.increment_download_count(file_obj.id)
        
        # Return file
        return StreamingResponse(
            file_data,
            media_type=file_obj.mime_type or "application/octet-stream",
            headers={
                "Content-Disposition": f"attachment; filename={file_obj.filename}"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to download file: {str(e)}"
        )


@router.get("/", response_model=List[dict])
async def get_files(
    skip: int = 0,
    limit: int = 100,
    file_type: Optional[FileType] = None,
    status: Optional[FileStatus] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get user's files"""
    file_service = FileService(db)
    files = await file_service.get_by_owner(
        owner_id=current_user.id,
        skip=skip,
        limit=limit,
        file_type=file_type,
        status=status
    )
    return [file.to_dict() for file in files]


@router.get("/{file_id}", response_model=dict)
async def get_file(
    file_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get file by ID"""
    file_service = FileService(db)
    
    try:
        file_obj = await file_service.get_by_id(file_id)
        if not file_obj:
            raise NotFoundError(f"File with ID {file_id} not found")
        
        # Check permissions
        if file_obj.owner_id != current_user.id and not file_obj.is_public:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Increment view count
        await file_service.increment_view_count(file_id)
        
        return file_obj.to_dict()
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )


@router.delete("/{file_id}")
async def delete_file(
    file_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Delete file"""
    file_service = FileService(db)
    
    try:
        file_obj = await file_service.get_by_id(file_id)
        if not file_obj:
            raise NotFoundError(f"File with ID {file_id} not found")
        
        # Check permissions
        if file_obj.owner_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Delete from storage
        await file_service.delete_from_storage(file_id)
        
        # Delete from database
        await file_service.delete(file_id)
        
        return {"message": "File deleted successfully"}
    except NotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )


@router.get("/stats/storage")
async def get_storage_stats(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get storage statistics"""
    file_service = FileService(db)
    
    # Get user file stats
    user_stats = await file_service.get_file_stats(owner_id=current_user.id)
    
    # Get storage system stats
    storage_stats = local_storage.get_storage_stats()
    
    return {
        "user_stats": user_stats,
        "storage_stats": storage_stats
    }


def determine_file_type(file_extension: str) -> FileType:
    """Determine file type from extension"""
    image_types = ["jpg", "jpeg", "png", "gif", "bmp", "webp"]
    document_types = ["pdf", "doc", "docx", "txt", "rtf"]
    data_types = ["csv", "json", "xml", "xlsx", "xls"]
    
    if file_extension.lower() in image_types:
        return FileType.IMAGE
    elif file_extension.lower() in document_types:
        return FileType.DOCUMENT
    elif file_extension.lower() in data_types:
        return FileType.DATA
    else:
        return FileType.OTHER
