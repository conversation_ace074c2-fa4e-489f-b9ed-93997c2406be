"""Super Agent Chat API - Single agent chat interface"""

from typing import List, AsyncGenerator, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel
from datetime import datetime
import json
from loguru import logger

from app.core.database import get_async_db
from app.core.security import get_current_user
from app.models.user import User
from app.services.super_agent_service import super_agent_service
from app.agents.base_agent import AgentMessage, AgentContext

router = APIRouter()


class ChatMessage(BaseModel):
    role: str
    content: str
    timestamp: datetime = None
    metadata: dict = {}


class ChatRequest(BaseModel):
    messages: List[ChatMessage]
    stream: bool = True
    context: dict = {}


class ChatResponse(BaseModel):
    content: str
    success: bool
    error: Optional[str] = None
    metadata: dict = {}
    timestamp: datetime


@router.get("/info")
async def get_super_agent_info(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get super agent information"""
    try:
        info = await super_agent_service.get_super_agent_info(db)
        return info
    except Exception as e:
        logger.error(f"Failed to get super agent info: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get super agent information"
        )


@router.post("/chat")
async def chat_with_super_agent(
    request: ChatRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Chat with the super agent"""
    try:
        # Get super agent instance
        super_agent = await super_agent_service.get_super_agent_instance(db)
        
        # Convert request messages to agent messages
        agent_messages = [
            AgentMessage(
                role=msg.role,
                content=msg.content,
                timestamp=msg.timestamp or datetime.utcnow(),
                metadata=msg.metadata
            )
            for msg in request.messages
        ]
        
        # Create execution context
        context = AgentContext(
            agent_id=super_agent.agent.id,
            user_id=current_user.id,
            conversation_id=request.context.get("conversation_id"),
            task_id=request.context.get("task_id"),
            files=request.context.get("files", []),
            variables=request.context.get("variables", {})
        )
        
        if request.stream:
            # Streaming response
            async def generate_response():
                try:
                    async for response in super_agent.execute(agent_messages, context):
                        response_data = {
                            "content": response.content,
                            "success": response.success,
                            "error": response.error,
                            "metadata": response.metadata or {},
                            "timestamp": datetime.utcnow().isoformat()
                        }
                        yield f"data: {json.dumps(response_data)}\n\n"
                except Exception as e:
                    error_response = {
                        "content": "",
                        "success": False,
                        "error": str(e),
                        "metadata": {},
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    yield f"data: {json.dumps(error_response)}\n\n"
            
            return StreamingResponse(
                generate_response(),
                media_type="text/plain",
                headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
            )
        else:
            # Non-streaming response
            responses = []
            async for response in super_agent.execute(agent_messages, context):
                responses.append(ChatResponse(
                    content=response.content,
                    success=response.success,
                    error=response.error,
                    metadata=response.metadata or {},
                    timestamp=datetime.utcnow()
                ))
            
            return {"responses": responses}
            
    except Exception as e:
        logger.error(f"Chat execution failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Chat execution failed: {str(e)}"
        )


@router.get("/status")
async def get_super_agent_status(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get super agent status"""
    try:
        agent_model = await super_agent_service.get_or_create_super_agent(db)
        return {
            "status": agent_model.status.value,
            "is_active": agent_model.is_active,
            "total_executions": agent_model.total_executions,
            "successful_executions": agent_model.successful_executions,
            "failed_executions": agent_model.failed_executions,
            "success_rate": agent_model.success_rate,
            "average_execution_time": agent_model.average_execution_time,
            "last_executed_at": agent_model.last_executed_at.isoformat() if agent_model.last_executed_at else None
        }
    except Exception as e:
        logger.error(f"Failed to get super agent status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get super agent status"
        )


@router.get("/capabilities")
async def get_super_agent_capabilities(
    current_user: User = Depends(get_current_user)
):
    """Get super agent capabilities"""
    return {
        "capabilities": [
            {
                "name": "Text Generation & Conversation",
                "description": "Natural language understanding and generation for conversations, Q&A, and explanations",
                "icon": "💬"
            },
            {
                "name": "Code Development & Debugging", 
                "description": "Write, review, debug, and optimize code in any programming language",
                "icon": "🔧"
            },
            {
                "name": "Research & Web Browsing",
                "description": "Gather information from the web, analyze sources, and provide comprehensive research",
                "icon": "🔍"
            },
            {
                "name": "Data Analysis & Visualization",
                "description": "Process datasets, perform statistical analysis, and create visualizations",
                "icon": "📊"
            },
            {
                "name": "File Processing & Management",
                "description": "Read, write, and manipulate various file formats and data structures",
                "icon": "📁"
            },
            {
                "name": "Image Generation & Processing",
                "description": "Create and manipulate images, diagrams, and visual content",
                "icon": "🎨"
            },
            {
                "name": "API Integration & Automation",
                "description": "Connect with external services and automate workflows",
                "icon": "⚙️"
            },
            {
                "name": "Workflow Design & Optimization",
                "description": "Design efficient processes and optimize existing workflows",
                "icon": "🔄"
            }
        ]
    }