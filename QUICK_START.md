# 🚀 Agentico Quick Start Guide

Welcome to Agentico! This guide will get you up and running in minutes.

## 📋 Prerequisites

Before you begin, ensure you have:

- **Docker Desktop** installed and running
- **Git** for cloning the repository
- **8GB+ RAM** recommended
- **10GB+ free disk space**

### Install Docker Desktop

1. **Windows**: Download from [docker.com](https://www.docker.com/products/docker-desktop/)
2. **macOS**: Download from [docker.com](https://www.docker.com/products/docker-desktop/)
3. **Linux**: Follow [Docker installation guide](https://docs.docker.com/engine/install/)

## 🎯 Quick Deployment

### Option 1: Automated Deployment (Recommended)

**Windows:**
```bash
# Clone the repository
git clone <repository-url>
cd agentico

# Run the deployment script
deploy.bat
```

**macOS/Linux:**
```bash
# Clone the repository
git clone <repository-url>
cd agentico

# Make script executable and run
chmod +x deploy.sh
./deploy.sh
```

### Option 2: Manual Deployment

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd agentico
   cp .env.example .env
   ```

2. **Edit Configuration**
   - Open `.env` file in your text editor
   - Add your OpenAI API key (optional but recommended):
     ```
     OPENAI_API_KEY=your-openai-api-key-here
     ```
   - Change the JWT secret key for security

3. **Deploy**
   ```bash
   docker-compose up -d
   ```

4. **Wait for Services**
   - Wait 2-3 minutes for all services to start
   - Check status: `docker-compose ps`

## 🌐 Access the Platform

Once deployed, access these URLs:

- **🎯 Main Application**: http://localhost:3000
- **📚 API Documentation**: http://localhost:8000/docs
- **🗄️ MinIO Console**: http://localhost:9001 (minioadmin/minioadmin)

## 👤 First Steps

### 1. Create Your Account
1. Visit http://localhost:3000
2. Click "Sign Up" 
3. Fill in your details
4. Verify your account

### 2. Create Your First Agent
1. Go to Dashboard → Agents
2. Click "Create Agent"
3. Choose agent type:
   - **General**: For conversations and general tasks
   - **Code**: For programming assistance
   - **Research**: For analysis and research
4. Configure your agent:
   - Name: "My First Agent"
   - Description: "A helpful AI assistant"
   - Model: "gpt-3.5-turbo" (if you have OpenAI API key)
5. Click "Create"

### 3. Start a Conversation
1. Go to Dashboard → Conversations
2. Click "Start Conversation"
3. Select your agent
4. Start chatting!

## 🔧 Configuration

### AI API Keys (Optional but Recommended)

To enable AI functionality, add your API keys to `.env`:

```bash
# OpenAI (recommended)
OPENAI_API_KEY=sk-your-openai-key-here

# Anthropic (optional)
ANTHROPIC_API_KEY=your-anthropic-key-here
```

### Security Settings

**Important**: Change these in production:

```bash
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
NEXTAUTH_SECRET=your-super-secret-nextauth-key-change-in-production
```

## 📊 Monitoring

### Check Service Status
```bash
docker-compose ps
```

### View Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f celery-worker
```

### Health Checks
- Backend Health: http://localhost:8000/health
- Database: Check logs for connection status
- Redis: Check logs for connection status

## 🛠️ Management Commands

### Start/Stop Services
```bash
# Start all services
docker-compose up -d

# Stop all services
docker-compose down

# Restart specific service
docker-compose restart backend
```

### Update the Platform
```bash
git pull
docker-compose build
docker-compose up -d
```

### Reset Everything
```bash
docker-compose down -v  # WARNING: This deletes all data!
docker-compose up -d
```

## 🐛 Troubleshooting

### Common Issues

**1. Port Already in Use**
```bash
# Check what's using the port
netstat -ano | findstr :3000  # Windows
lsof -i :3000                 # macOS/Linux

# Stop the conflicting service or change ports in docker-compose.yml
```

**2. Docker Out of Memory**
- Increase Docker Desktop memory allocation to 4GB+
- Close other applications

**3. Services Won't Start**
```bash
# Check logs
docker-compose logs

# Rebuild images
docker-compose build --no-cache
docker-compose up -d
```

**4. Database Connection Issues**
```bash
# Reset database
docker-compose down
docker volume rm agentico_postgres_data
docker-compose up -d
```

### Getting Help

1. **Check Logs**: Always check `docker-compose logs` first
2. **GitHub Issues**: Report bugs on the repository
3. **Documentation**: Check the full documentation
4. **Community**: Join our Discord/Slack community

## 🎉 You're Ready!

Congratulations! You now have a fully functional AI agent platform running locally. 

### What's Next?

1. **Explore Features**: Try different agent types and capabilities
2. **Upload Files**: Test file processing and analysis
3. **Create Workflows**: Build complex multi-agent workflows
4. **Customize**: Modify agents and add your own capabilities
5. **Deploy to Production**: Use our production deployment guide

### Key Features to Try

- ✅ **Real-time Chat**: Instant responses from AI agents
- ✅ **File Processing**: Upload and analyze documents
- ✅ **Multi-Agent System**: Different agents for different tasks
- ✅ **Task Management**: Background processing and monitoring
- ✅ **WebSocket Integration**: Live updates and notifications

Happy building with Agentico! 🚀
