"""
Custom exceptions for the Agentico application
"""

from typing import Optional


class AgenticoException(Exception):
    """Base exception for Agentico application"""
    
    def __init__(
        self,
        detail: str,
        status_code: int = 500,
        error_code: str = "INTERNAL_ERROR"
    ):
        self.detail = detail
        self.status_code = status_code
        self.error_code = error_code
        super().__init__(detail)


class AuthenticationError(AgenticoException):
    """Authentication related errors"""
    
    def __init__(self, detail: str = "Authentication failed"):
        super().__init__(
            detail=detail,
            status_code=401,
            error_code="AUTHENTICATION_ERROR"
        )


class AuthorizationError(AgenticoException):
    """Authorization related errors"""
    
    def __init__(self, detail: str = "Insufficient permissions"):
        super().__init__(
            detail=detail,
            status_code=403,
            error_code="AUTHORIZATION_ERROR"
        )


class ValidationError(AgenticoException):
    """Validation related errors"""
    
    def __init__(self, detail: str = "Validation failed"):
        super().__init__(
            detail=detail,
            status_code=422,
            error_code="VALIDATION_ERROR"
        )


class NotFoundError(AgenticoException):
    """Resource not found errors"""
    
    def __init__(self, detail: str = "Resource not found"):
        super().__init__(
            detail=detail,
            status_code=404,
            error_code="NOT_FOUND"
        )


class ConflictError(AgenticoException):
    """Resource conflict errors"""
    
    def __init__(self, detail: str = "Resource conflict"):
        super().__init__(
            detail=detail,
            status_code=409,
            error_code="CONFLICT_ERROR"
        )


class RateLimitError(AgenticoException):
    """Rate limit exceeded errors"""
    
    def __init__(self, detail: str = "Rate limit exceeded"):
        super().__init__(
            detail=detail,
            status_code=429,
            error_code="RATE_LIMIT_ERROR"
        )


class AgentError(AgenticoException):
    """Agent execution related errors"""
    
    def __init__(self, detail: str = "Agent execution failed"):
        super().__init__(
            detail=detail,
            status_code=500,
            error_code="AGENT_ERROR"
        )


class AgentTimeoutError(AgentError):
    """Agent execution timeout errors"""
    
    def __init__(self, detail: str = "Agent execution timed out"):
        super().__init__(detail=detail)
        self.error_code = "AGENT_TIMEOUT_ERROR"


class AgentNotFoundError(NotFoundError):
    """Agent not found errors"""
    
    def __init__(self, agent_id: str):
        super().__init__(detail=f"Agent with ID {agent_id} not found")
        self.error_code = "AGENT_NOT_FOUND"


class TaskError(AgenticoException):
    """Task execution related errors"""
    
    def __init__(self, detail: str = "Task execution failed"):
        super().__init__(
            detail=detail,
            status_code=500,
            error_code="TASK_ERROR"
        )


class TaskNotFoundError(NotFoundError):
    """Task not found errors"""
    
    def __init__(self, task_id: str):
        super().__init__(detail=f"Task with ID {task_id} not found")
        self.error_code = "TASK_NOT_FOUND"


class FileStorageError(AgenticoException):
    """File storage related errors"""
    
    def __init__(self, detail: str = "File storage operation failed"):
        super().__init__(
            detail=detail,
            status_code=500,
            error_code="FILE_STORAGE_ERROR"
        )


class DatabaseError(AgenticoException):
    """Database related errors"""
    
    def __init__(self, detail: str = "Database operation failed"):
        super().__init__(
            detail=detail,
            status_code=500,
            error_code="DATABASE_ERROR"
        )
