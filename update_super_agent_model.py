import asyncio
from app.core.database import get_async_db
from app.models.agent import Agent, AgentType
from sqlalchemy import select, update

async def update_model():
    async for db in get_async_db():
        # Find the super agent
        result = await db.execute(
            select(Agent).where(Agent.agent_type == AgentType.SUPER)
        )
        agent = result.scalar_one_or_none()
        
        if agent:
            print(f'Current model: {agent.model}')
            # Update to OpenRouter model
            await db.execute(
                update(Agent)
                .where(Agent.id == agent.id)
                .values(model='openrouter/anthropic/claude-3.5-sonnet')
            )
            await db.commit()
            print('Updated model to OpenRouter')
        else:
            print('No super agent found')
        break

if __name__ == '__main__':
    asyncio.run(update_model())