"""
Base agent class for all agent implementations
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, AsyncGenerator
from dataclasses import dataclass
from enum import Enum
import asyncio
import json
from datetime import datetime
from loguru import logger

from app.models.agent import Agent
from app.core.config import settings


class AgentCapability(str, Enum):
    """Agent capabilities enumeration"""
    TEXT_GENERATION = "text_generation"
    CODE_EXECUTION = "code_execution"
    WEB_BROWSING = "web_browsing"
    FILE_PROCESSING = "file_processing"
    IMAGE_GENERATION = "image_generation"
    DATA_ANALYSIS = "data_analysis"
    API_INTEGRATION = "api_integration"
    WORKFLOW_AUTOMATION = "workflow_automation"


@dataclass
class AgentMessage:
    """Agent message structure"""
    role: str
    content: str
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class AgentResponse:
    """Agent response structure"""
    content: str
    success: bool
    metadata: Optional[Dict[str, Any]] = None
    tool_calls: Optional[List[Dict[str, Any]]] = None
    error: Optional[str] = None


@dataclass
class AgentContext:
    """Agent execution context"""
    agent_id: int
    user_id: int
    conversation_id: Optional[int] = None
    task_id: Optional[int] = None
    files: Optional[List[Dict[str, Any]]] = None
    variables: Optional[Dict[str, Any]] = None


class BaseAgent(ABC):
    """Base class for all agents"""
    
    def __init__(self, agent: Agent):
        self.agent = agent
        self.capabilities = agent.capabilities or []
        self.tools = agent.tools or []
        self.config = agent.config or {}
        self.environment_vars = agent.environment_vars or {}
        
    @abstractmethod
    async def execute(
        self,
        messages: List[AgentMessage],
        context: AgentContext
    ) -> AsyncGenerator[AgentResponse, None]:
        """Execute agent with given messages and context"""
        pass
    
    @abstractmethod
    async def validate_input(self, messages: List[AgentMessage]) -> bool:
        """Validate input messages"""
        pass
    
    def has_capability(self, capability: AgentCapability) -> bool:
        """Check if agent has specific capability"""
        return capability.value in self.capabilities
    
    def get_tool(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """Get tool configuration by name"""
        for tool in self.tools:
            if tool.get("name") == tool_name:
                return tool
        return None
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        return self.config.get(key, default)
    
    def get_env_var(self, key: str, default: str = None) -> str:
        """Get environment variable"""
        return self.environment_vars.get(key, default)
    
    async def preprocess_messages(
        self,
        messages: List[AgentMessage]
    ) -> List[Dict[str, Any]]:
        """Preprocess messages for LLM"""
        processed = []
        for msg in messages:
            processed.append({
                "role": msg.role,
                "content": msg.content,
                "timestamp": msg.timestamp.isoformat()
            })
        return processed
    
    async def postprocess_response(
        self,
        response: str,
        metadata: Dict[str, Any] = None
    ) -> AgentResponse:
        """Postprocess LLM response"""
        return AgentResponse(
            content=response,
            success=True,
            metadata=metadata or {}
        )
    
    def get_system_prompt(self) -> str:
        """Get system prompt for the agent"""
        base_prompt = self.agent.system_prompt or ""
        
        # Add capability information
        if self.capabilities:
            capabilities_text = ", ".join(self.capabilities)
            base_prompt += f"\n\nYou have the following capabilities: {capabilities_text}"
        
        # Add tool information
        if self.tools:
            tools_text = "\n".join([f"- {tool.get('name', 'Unknown')}: {tool.get('description', 'No description')}" for tool in self.tools])
            base_prompt += f"\n\nAvailable tools:\n{tools_text}"
        
        return base_prompt
    
    async def handle_error(self, error: Exception, context: AgentContext) -> AgentResponse:
        """Handle execution errors"""
        logger.error(f"Agent {self.agent.id} error: {error}")
        
        return AgentResponse(
            content="I encountered an error while processing your request. Please try again.",
            success=False,
            error=str(error),
            metadata={"error_type": type(error).__name__}
        )
    
    async def log_execution(
        self,
        context: AgentContext,
        messages: List[AgentMessage],
        response: AgentResponse,
        execution_time: float
    ):
        """Log agent execution"""
        log_data = {
            "agent_id": self.agent.id,
            "user_id": context.user_id,
            "conversation_id": context.conversation_id,
            "task_id": context.task_id,
            "message_count": len(messages),
            "success": response.success,
            "execution_time": execution_time,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        logger.info(f"Agent execution completed: {json.dumps(log_data)}")
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}(id={self.agent.id}, name='{self.agent.name}')"
    
    def __repr__(self) -> str:
        return self.__str__()
