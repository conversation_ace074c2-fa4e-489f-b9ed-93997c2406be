"""Super Agent Service - Manages the single global super agent"""

from typing import Op<PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from loguru import logger

from app.models.agent import Agent, AgentType, AgentStatus
from app.models.user import User
from app.core.database import get_async_db
from app.agents.super_agent import SuperAgent
from app.agents.agent_factory import AgentFactory


class SuperAgentService:
    """Service for managing the global super agent"""
    
    _instance = None
    _super_agent_instance = None
    _super_agent_model = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(SuperAgentService, cls).__new__(cls)
        return cls._instance
    
    async def get_or_create_super_agent(self, db: AsyncSession) -> Agent:
        """Get or create the global super agent"""
        if self._super_agent_model is not None:
            return self._super_agent_model
        
        # Check if super agent already exists
        result = await db.execute(
            select(Agent).where(
                Agent.agent_type == AgentType.SUPER,
                Agent.name == "Universal AI Assistant"
            )
        )
        existing_agent = result.scalar_one_or_none()
        
        if existing_agent:
            self._super_agent_model = existing_agent
            logger.info(f"Found existing super agent: {existing_agent.id}")
            return existing_agent
        
        # Create the global super agent
        try:
            super_agent = Agent(
                name="Universal AI Assistant",
                description="A powerful AI assistant that adapts to handle any task - coding, research, analysis, creativity, automation, and more. This agent intelligently selects the right capabilities based on your needs.",
                agent_type=AgentType.SUPER,
                model="gpt-4",
                system_prompt="""You are a Universal AI Assistant with comprehensive capabilities across multiple domains. You can:

🔧 **Code & Development**: Write, debug, and optimize code in any programming language
🔍 **Research & Analysis**: Gather information, analyze data, and provide insights
🎨 **Creative Tasks**: Generate content, write stories, create marketing copy
📊 **Data Analysis**: Process and visualize data, create reports and dashboards
⚙️ **Automation**: Design workflows, integrate APIs, optimize processes
💬 **General Assistance**: Answer questions, provide explanations, solve problems

You automatically adapt your approach based on the user's request, selecting the most appropriate capabilities and tools. Always provide helpful, accurate, and well-structured responses.""",
                temperature="0.7",
                max_tokens=4096,
                status=AgentStatus.IDLE,
                is_active=True,
                is_public=True,
                max_execution_time=300,
                max_memory_mb=1024,
                max_cpu_percent=80,
                config={
                    "execution_mode": "adaptive",
                    "capability_weights": {
                        "text_generation": 1.0,
                        "code_execution": 1.0,
                        "web_browsing": 1.0,
                        "data_analysis": 1.0,
                        "file_processing": 1.0,
                        "image_generation": 0.8,
                        "api_integration": 0.9,
                        "workflow_automation": 0.9
                    }
                },
                owner_id=1  # System owned
            )
            
            db.add(super_agent)
            await db.commit()
            await db.refresh(super_agent)
            logger.info(f"Successfully created super agent: {super_agent.id}")
        except Exception as e:
            logger.error(f"Failed to create super agent: {e}")
            await db.rollback()
            raise
        
        self._super_agent_model = super_agent
        logger.info(f"Created new super agent: {super_agent.id}")
        return super_agent
    
    async def get_super_agent_instance(self, db: AsyncSession) -> SuperAgent:
        """Get the super agent instance for execution"""
        if self._super_agent_instance is not None:
            return self._super_agent_instance
        
        # Get or create the agent model
        agent_model = await self.get_or_create_super_agent(db)
        
        # Create the agent instance
        self._super_agent_instance = AgentFactory.create_agent(agent_model)
        
        logger.info("Super agent instance ready for execution")
        return self._super_agent_instance
    
    async def get_super_agent_info(self, db: AsyncSession) -> dict:
        """Get super agent information for frontend"""
        agent = await self.get_or_create_super_agent(db)
        return {
            "id": agent.id,
            "name": agent.name,
            "description": agent.description,
            "status": agent.status.value,
            "capabilities": [
                "Text Generation & Conversation",
                "Code Development & Debugging", 
                "Research & Web Browsing",
                "Data Analysis & Visualization",
                "File Processing & Management",
                "Image Generation & Processing",
                "API Integration & Automation",
                "Workflow Design & Optimization"
            ],
            "model": agent.model,
            "total_executions": agent.total_executions,
            "success_rate": agent.success_rate,
            "average_execution_time": agent.average_execution_time
        }
    
    def reset_instance(self):
        """Reset the singleton instance (for testing)"""
        self._super_agent_instance = None
        self._super_agent_model = None


# Global service instance
super_agent_service = SuperAgentService()