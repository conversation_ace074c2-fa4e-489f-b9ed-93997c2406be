# OpenRouter Integration Guide

This guide explains how to integrate OpenRouter with Agentico to access a wide variety of LLM models through a single API.

## What is OpenRouter?

OpenRouter is a unified API that provides access to multiple LLM providers including:

- OpenAI (GPT-4, GPT-3.5)
- Anthropic (<PERSON>)
- Google (Gemini)
- Meta (Llama)
- Mistral AI
- And many more

## Setup Instructions

### 1. Get OpenRouter API Key

1. Visit [OpenRouter](https://openrouter.ai/)
2. Sign up for an account
3. Navigate to the API Keys section
4. Generate a new API key

### 2. Configure Environment Variables

Add your OpenRouter API key to your `.env` file:

```bash
# OpenRouter Configuration
OPENROUTER_API_KEY=your-openrouter-api-key-here
```

### 3. Using OpenRouter Models

OpenRouter models can be used by specifying the model name in the format `openrouter/provider/model`. Here are some popular examples:

#### OpenAI Models via OpenRouter

```
openrouter/openai/gpt-4
openrouter/openai/gpt-3.5-turbo
openrouter/openai/gpt-4-turbo
```

#### Anthropic Models via OpenRouter

```
openrouter/anthropic/claude-3-opus
openrouter/anthropic/claude-3-sonnet
openrouter/anthropic/claude-3-haiku
```

#### Google Models via OpenRouter

```
openrouter/google/gemini-pro
openrouter/google/gemini-pro-vision
```

#### Meta Models via OpenRouter

```
openrouter/meta-llama/llama-2-70b-chat
openrouter/meta-llama/codellama-34b-instruct
```

#### Mistral Models via OpenRouter

```
openrouter/mistralai/mistral-7b-instruct
openrouter/mistralai/mixtral-8x7b-instruct
```

### 4. Update Super Agent Configuration

You can configure the super agent to use OpenRouter models by:

#### Option A: Update Default Model

In your `.env` file:

```bash
DEFAULT_LLM_MODEL=openrouter/anthropic/claude-3-sonnet
```

#### Option B: Update Agent Model via API

When creating or updating an agent, specify the OpenRouter model:

```json
{
  "name": "Claude Super Agent",
  "model": "openrouter/anthropic/claude-3-opus",
  "agent_type": "super"
}
```

## Model Selection Guidelines

### For General Tasks

- `openrouter/openai/gpt-4` - Best overall performance
- `openrouter/anthropic/claude-3-sonnet` - Great balance of speed and quality
- `openrouter/google/gemini-pro` - Good alternative with competitive pricing

### For Code Generation

- `openrouter/openai/gpt-4` - Excellent code understanding
- `openrouter/meta-llama/codellama-34b-instruct` - Specialized for coding
- `openrouter/anthropic/claude-3-opus` - Strong reasoning for complex code

### For Cost-Effective Solutions

- `openrouter/openai/gpt-3.5-turbo` - Fast and affordable
- `openrouter/mistralai/mistral-7b-instruct` - Open-source alternative
- `openrouter/anthropic/claude-3-haiku` - Fast Claude variant

### For Specialized Tasks

- `openrouter/google/gemini-pro-vision` - Image understanding
- `openrouter/meta-llama/llama-2-70b-chat` - Open-source large model

## Benefits of Using OpenRouter

1. **Model Diversity**: Access to 50+ models from different providers
2. **Unified API**: Single integration for multiple providers
3. **Cost Optimization**: Compare pricing across providers
4. **Fallback Options**: Switch models if one is unavailable
5. **Rate Limiting**: Built-in rate limiting and load balancing

## Pricing Considerations

OpenRouter provides transparent pricing for all models. Check the [OpenRouter pricing page](https://openrouter.ai/models) for current rates.

## Testing Your Integration

1. Restart your backend after adding the API key:

   ```bash
   docker compose restart backend
   ```

2. Test the super agent with an OpenRouter model:
   ```bash
   curl -X POST \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -d '{"messages":[{"role":"user","content":"Hello from OpenRouter!"}]}' \
     http://localhost:8000/api/v1/super-agent/chat
   ```

## Troubleshooting

### Common Issues

1. **Invalid API Key**: Ensure your OpenRouter API key is correct
2. **Model Not Found**: Verify the model name format is correct
3. **Rate Limits**: OpenRouter has built-in rate limiting
4. **Insufficient Credits**: Check your OpenRouter account balance

### Debug Steps

1. Check backend logs for detailed error messages:

   ```bash
   docker compose logs backend
   ```

2. Verify environment variables are loaded:
   ```bash
   docker exec -it agentico-backend-1 env | grep OPENROUTER
   ```

## Advanced Configuration

### Custom Model Parameters

You can customize model parameters when using OpenRouter models:

```python
# In your agent configuration
{
    "model": "openrouter/anthropic/claude-3-opus",
    "temperature": "0.7",
    "max_tokens": 4096
}
```

### Model Fallbacks

Consider implementing model fallbacks in your application logic to handle cases where a specific model might be unavailable.

## Support

For OpenRouter-specific issues:

- [OpenRouter Documentation](https://openrouter.ai/docs)
- [OpenRouter Discord](https://discord.gg/openrouter)

For Agentico integration issues:

- Check the backend logs
- Review this documentation
- Ensure all environment variables are properly set
