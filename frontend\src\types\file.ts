export interface FileItem {
  id: number;
  filename: string;
  original_filename: string;
  file_type: FileType;
  mime_type: string;
  file_size: number;
  file_size_mb: number;
  file_hash: string;
  storage_path: string;
  status: FileStatus;
  is_public: boolean;
  is_temporary: boolean;
  extracted_text?: string;
  meta_data?: Record<string, any>;
  processing_status?: string;
  processing_error?: string;
  processing_progress: number;
  access_level: AccessLevel;
  shared_with: number[];
  download_count: number;
  view_count: number;
  expires_at?: string;
  auto_delete: boolean;
  created_at: string;
  updated_at: string;
  last_accessed_at?: string;
  owner_id: number;
  public_url?: string;
}

export enum FileType {
  DOCUMENT = 'document',
  IMAGE = 'image',
  AUDIO = 'audio',
  VIDEO = 'video',
  CODE = 'code',
  DATA = 'data',
  ARCHIVE = 'archive',
  OTHER = 'other',
}

export enum FileStatus {
  UPLOADING = 'uploading',
  PROCESSING = 'processing',
  READY = 'ready',
  ERROR = 'error',
  DELETED = 'deleted',
}

export enum AccessLevel {
  PRIVATE = 'private',
  SHARED = 'shared',
  PUBLIC = 'public',
}

export interface FileUploadResponse {
  id: number;
  filename: string;
  file_size: number;
  content_type: string;
  status: string;
  public_url?: string;
  message: string;
}

export interface FileStats {
  total_count: number;
  total_size_bytes: number;
  total_size_mb: number;
  ready_count: number;
  processing_count: number;
  error_count: number;
  uploading_count: number;
  deleted_count: number;
  document_count: number;
  image_count: number;
  audio_count: number;
  video_count: number;
  code_count: number;
  data_count: number;
  archive_count: number;
  other_count: number;
}

export interface FileListParams {
  skip?: number;
  limit?: number;
  file_type?: FileType;
  status?: FileStatus;
}

export interface FileSearchParams {
  query: string;
  skip?: number;
  limit?: number;
}

export interface FileShareRequest {
  user_id: number;
}

export interface FileMetadata {
  storage_type: string;
  upload_ip: string;
  user_agent: string;
  [key: string]: any;
}

export interface FileProcessingInfo {
  status: string;
  progress: number;
  error?: string;
  started_at?: string;
  completed_at?: string;
}

export interface FilePermissions {
  can_read: boolean;
  can_write: boolean;
  can_delete: boolean;
  can_share: boolean;
  is_owner: boolean;
}

export interface FilePreview {
  type: 'image' | 'video' | 'audio' | 'document' | 'text' | 'unsupported';
  url?: string;
  thumbnail_url?: string;
  content?: string;
  duration?: number;
  dimensions?: {
    width: number;
    height: number;
  };
}

export interface FileUploadProgress {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
  result?: FileUploadResponse;
}

export interface FileFilter {
  type?: FileType;
  status?: FileStatus;
  access_level?: AccessLevel;
  date_range?: {
    start: string;
    end: string;
  };
  size_range?: {
    min: number;
    max: number;
  };
}

export interface FileSortOptions {
  field: 'filename' | 'file_size' | 'created_at' | 'updated_at' | 'download_count' | 'view_count';
  direction: 'asc' | 'desc';
}

export interface FileListResponse {
  files: FileItem[];
  total: number;
  skip: number;
  limit: number;
  has_more: boolean;
}

export interface FileActionResult {
  success: boolean;
  message: string;
  file?: FileItem;
}

// File validation
export interface FileValidationRules {
  maxSize: number; // in bytes
  allowedTypes: string[];
  allowedExtensions: string[];
}

export interface FileValidationError {
  field: string;
  message: string;
  code: string;
}

// File operations
export interface FileBulkOperation {
  action: 'delete' | 'make_public' | 'make_private' | 'archive';
  file_ids: number[];
}

export interface FileBulkOperationResult {
  success_count: number;
  error_count: number;
  errors: Array<{
    file_id: number;
    error: string;
  }>;
}
