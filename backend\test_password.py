from app.core.security import verify_password

# The actual hashed password from database
hashed_password = "$2b$12$fRQzmvrlNY5XJrTkwEwemOIQyAdeaT44Ywh3FNXIqje6tylQEpFP2"

# Test different possible passwords
test_passwords = [
    "axdsan92!",
    "axdsan92",
    "axdsan92!!"
]

print("Testing password verification:")
for pwd in test_passwords:
    result = verify_password(pwd, hashed_password)
    print(f"Password '{pwd}': {'✓ MATCH' if result else '✗ NO MATCH'}")

print("\nPlease try entering one of the passwords that shows '✓ MATCH' above.")