# 🚀 Agentico Deployment Guide

## 📋 System Requirements

### Minimum Requirements
- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 10GB free space
- **OS**: Windows 10+, macOS 10.15+, or Linux

### Recommended Requirements
- **CPU**: 4+ cores
- **RAM**: 8GB+
- **Storage**: 20GB+ free space
- **Network**: Stable internet connection

## 🛠️ Prerequisites

### 1. Install Docker Desktop

**Windows:**
1. Download Docker Desktop from [docker.com](https://www.docker.com/products/docker-desktop/)
2. Run the installer
3. Restart your computer
4. Start Docker Desktop

**macOS:**
1. Download Docker Desktop from [docker.com](https://www.docker.com/products/docker-desktop/)
2. Drag Docker to Applications folder
3. Launch Docker Desktop

**Linux:**
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
# Log out and back in

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. Verify Docker Installation
```bash
docker --version
docker-compose --version
```

## 🚀 Quick Deployment

### Option 1: Automated Script (Recommended)

**Windows:**
```cmd
# Open Command Prompt or PowerShell as Administrator
git clone https://github.com/your-repo/agentico.git
cd agentico
deploy.bat
```

**macOS/Linux:**
```bash
git clone https://github.com/your-repo/agentico.git
cd agentico
chmod +x deploy.sh
./deploy.sh
```

### Option 2: Manual Deployment

1. **Clone Repository**
   ```bash
   git clone https://github.com/your-repo/agentico.git
   cd agentico
   ```

2. **Setup Environment**
   ```bash
   cp .env.example .env
   # Edit .env file with your configuration
   ```

3. **Deploy Services**
   ```bash
   docker-compose up -d
   ```

4. **Verify Deployment**
   ```bash
   python test-deployment.py
   ```

## ⚙️ Configuration

### Environment Variables

Edit `.env` file with your settings:

```bash
# AI API Keys (Optional but recommended)
OPENAI_API_KEY=your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# Security (CHANGE IN PRODUCTION!)
JWT_SECRET_KEY=your-super-secret-jwt-key
NEXTAUTH_SECRET=your-nextauth-secret

# Database
DATABASE_URL=********************************************/agentico

# Redis
REDIS_URL=redis://redis:6379

# MinIO
MINIO_ENDPOINT=minio:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin

# Application URLs
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
```

### AI API Keys Setup

**OpenAI API Key:**
1. Visit [platform.openai.com](https://platform.openai.com)
2. Create account and add payment method
3. Go to API Keys section
4. Create new secret key
5. Add to `.env` file

**Anthropic API Key:**
1. Visit [console.anthropic.com](https://console.anthropic.com)
2. Create account
3. Go to API Keys section
4. Create new key
5. Add to `.env` file

## 📊 Service Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Database      │
│   (Next.js)     │◄──►│   (FastAPI)     │◄──►│  (PostgreSQL)   │
│   Port: 3000    │    │   Port: 8000    │    │   Port: 5432    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐    ┌─────────────────┐
         │              │     Redis       │    │     MinIO       │
         └──────────────►│  (Cache/Queue)  │    │ (File Storage)  │
                        │   Port: 6379    │    │   Port: 9000    │
                        └─────────────────┘    └─────────────────┘
                                 │
                        ┌─────────────────┐
                        │ Celery Workers  │
                        │ (Background)    │
                        └─────────────────┘
```

## 🔍 Verification

### 1. Check Service Status
```bash
docker-compose ps
```

Expected output:
```
NAME                    COMMAND                  SERVICE             STATUS
agentico-backend        "uvicorn app.main:ap…"   backend             Up
agentico-celery-beat    "celery -A app.core.…"   celery-beat         Up
agentico-celery-worker  "celery -A app.core.…"   celery-worker       Up
agentico-frontend       "docker-entrypoint.s…"   frontend            Up
agentico-minio          "/usr/bin/docker-ent…"   minio               Up
agentico-postgres       "docker-entrypoint.s…"   postgres            Up
agentico-redis          "docker-entrypoint.s…"   redis               Up
```

### 2. Test Endpoints
```bash
# Backend health
curl http://localhost:8000/health

# Frontend
curl http://localhost:3000

# API documentation
open http://localhost:8000/docs
```

### 3. Run Automated Tests
```bash
python test-deployment.py
```

## 🌐 Access URLs

Once deployed, access these services:

| Service | URL | Credentials |
|---------|-----|-------------|
| **Main Application** | http://localhost:3000 | Create account |
| **API Documentation** | http://localhost:8000/docs | None |
| **Backend API** | http://localhost:8000 | None |
| **MinIO Console** | http://localhost:9001 | minioadmin/minioadmin |

## 🔧 Management

### Start/Stop Services
```bash
# Start all services
docker-compose up -d

# Stop all services
docker-compose down

# Restart specific service
docker-compose restart backend

# View logs
docker-compose logs -f backend
```

### Update Platform
```bash
git pull
docker-compose build
docker-compose up -d
```

### Backup Data
```bash
# Backup database
docker-compose exec postgres pg_dump -U postgres agentico > backup.sql

# Backup files
docker run --rm -v agentico_minio_data:/data -v $(pwd):/backup alpine tar czf /backup/minio-backup.tar.gz /data
```

### Reset Platform
```bash
# WARNING: This deletes all data!
docker-compose down -v
docker-compose up -d
```

## 🐛 Troubleshooting

### Common Issues

**1. Port Already in Use**
```bash
# Windows
netstat -ano | findstr :3000
# Kill process using the port

# macOS/Linux
lsof -i :3000
kill -9 <PID>
```

**2. Docker Out of Memory**
- Increase Docker Desktop memory to 4GB+
- Close other applications
- Restart Docker Desktop

**3. Services Won't Start**
```bash
# Check logs
docker-compose logs

# Rebuild images
docker-compose build --no-cache
docker-compose up -d
```

**4. Database Connection Issues**
```bash
# Reset database
docker-compose down
docker volume rm agentico_postgres_data
docker-compose up -d
```

**5. Frontend Build Issues**
```bash
# Clear Node modules and rebuild
docker-compose down
docker-compose build --no-cache frontend
docker-compose up -d
```

### Log Analysis

**View all logs:**
```bash
docker-compose logs -f
```

**View specific service logs:**
```bash
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f celery-worker
```

**Follow logs in real-time:**
```bash
docker-compose logs -f --tail=100
```

## 🔒 Security Considerations

### Development Environment
- Default passwords are used (change in production)
- CORS is open to all origins
- Debug mode is enabled

### Production Deployment
1. Change all default passwords
2. Use environment-specific secrets
3. Configure proper CORS origins
4. Enable HTTPS
5. Use production database
6. Set up monitoring and logging
7. Configure backup strategy

## 📈 Performance Optimization

### Resource Allocation
```yaml
# In docker-compose.yml, add resource limits:
services:
  backend:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
```

### Database Optimization
- Increase shared_buffers
- Configure connection pooling
- Set up read replicas for scaling

### Caching Strategy
- Redis for session storage
- Application-level caching
- CDN for static assets

## 🎉 Success!

If all tests pass, you now have:

✅ **Full-stack AI agent platform**
✅ **Real-time communication**
✅ **Background task processing**
✅ **File storage and processing**
✅ **Modern web interface**
✅ **API documentation**
✅ **Monitoring and health checks**

### Next Steps

1. **Create your first user account**
2. **Set up AI API keys**
3. **Create your first agent**
4. **Start a conversation**
5. **Explore advanced features**

Welcome to Agentico! 🚀
