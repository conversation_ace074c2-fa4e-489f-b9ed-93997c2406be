"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import {
  PlusIcon,
  ChatBubbleLeftIcon,
  MagnifyingGlassIcon,
  EllipsisVerticalIcon,
  TrashIcon,
  ArchiveBoxIcon,
  ClockIcon,
  UserIcon,
  FunnelIcon, // Added FunnelIcon here
} from "@heroicons/react/24/outline";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { conversationService } from "@/services/conversation-service";
import type { Conversation } from "@/types/conversation";

export default function ConversationsPage() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState("");
  const [showNewConversationModal, setShowNewConversationModal] =
    useState(false);
  const [newConversationTitle, setNewConversationTitle] = useState("");
  const [newConversationDescription, setNewConversationDescription] =
    useState("");

  // Fetch conversations
  const {
    data: conversations = [],
    isLoading,
    error,
  } = useQuery({
    queryKey: ["conversations"],
    queryFn: () => conversationService.getConversations(),
  });

  // Create conversation mutation
  const createConversationMutation = useMutation({
    mutationFn: conversationService.createConversation,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["conversations"] });
      toast.success("Conversation created successfully");
      setShowNewConversationModal(false);
      setNewConversationTitle("");
      setNewConversationDescription("");
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to create conversation");
    },
  });

  // Delete conversation mutation
  const deleteConversationMutation = useMutation({
    mutationFn: conversationService.deleteConversation,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["conversations"] });
      toast.success("Conversation deleted successfully");
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to delete conversation");
    },
  });

  // Archive conversation mutation
  const archiveConversationMutation = useMutation({
    mutationFn: conversationService.archiveConversation,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["conversations"] });
      toast.success("Conversation archived successfully");
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to archive conversation");
    },
  });

  const handleCreateConversation = () => {
    if (!newConversationTitle.trim()) {
      toast.error("Please enter a conversation title");
      return;
    }

    createConversationMutation.mutate({
      title: newConversationTitle,
      description: newConversationDescription || undefined,
    });
  };

  const handleDeleteConversation = (id: number) => {
    if (
      confirm(
        "Are you sure you want to delete this conversation? This action cannot be undone."
      )
    ) {
      deleteConversationMutation.mutate(id);
    }
  };

  const handleArchiveConversation = (id: number) => {
    archiveConversationMutation.mutate(id);
  };

  const filteredConversations = conversations.filter(
    (conversation) =>
      conversation.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (conversation.description &&
        conversation.description
          .toLowerCase()
          .includes(searchQuery.toLowerCase()))
  );

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "text-green-600 bg-green-100";
      case "archived":
        return "text-gray-600 bg-gray-100";
      case "completed":
        return "text-blue-600 bg-blue-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: "short" });
    } else {
      return date.toLocaleDateString([], { month: "short", day: "numeric" });
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="space-y-1">
          <h1 className="text-2xl font-bold text-gray-900">Conversations</h1>
          <p className="text-gray-600">Manage your AI conversations and chat history</p>
        </div>
        <Button
          onClick={() => setShowNewConversationModal(true)}
          className="flex items-center gap-2 w-full sm:w-auto"
        >
          <PlusIcon className="h-4 w-4" />
          New Conversation
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
        <div className="relative flex-1 max-w-md">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button variant="outline" className="flex items-center gap-2 w-full sm:w-auto">
          <FunnelIcon className="h-4 w-4" />
          Filters
        </Button>
      </div>

      {/* Conversations List */}
      {filteredConversations.length === 0 ? (
        <Card className="p-6">
          <div className="flex items-center justify-center h-64 text-gray-500">
            <div className="text-center">
              <ChatBubbleLeftIcon className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium mb-2">
                {searchQuery
                  ? "No conversations found"
                  : "No conversations yet"}
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                {searchQuery
                  ? "Try adjusting your search terms"
                  : "Start a new conversation with an AI agent to see it here."}
              </p>
              {!searchQuery && (
                <Button
                  onClick={() => setShowNewConversationModal(true)}
                  variant="outline"
                >
                  Start Conversation
                </Button>
              )}
            </div>
          </div>
        </Card>
      ) : (
        <div className="grid gap-4">
          {filteredConversations.map((conversation) => (
            <Card
              key={conversation.id}
              className="p-4 hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => router.push(`/dashboard/conversations/${conversation.id}`)}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="font-medium text-gray-900">
                      {conversation.title}
                    </h3>
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${getStatusColor(conversation.status)}`}
                    >
                      {conversation.status}
                    </span>
                  </div>
                  {conversation.description && (
                    <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                      {conversation.description}
                    </p>
                  )}
                  <div className="flex flex-wrap items-center gap-4 text-xs text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <ClockIcon className="h-3 w-3" />
                      <span>{formatDate(conversation.updated_at)}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <UserIcon className="h-3 w-3" />
                      <span>{conversation.message_count || 0} messages</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2 flex-shrink-0">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleArchiveConversation(conversation.id);
                    }}
                    disabled={archiveConversationMutation.isPending}
                    className="h-8 w-8 p-0"
                  >
                    <ArchiveBoxIcon className="h-4 w-4" />
                    <span className="sr-only">Archive conversation</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteConversation(conversation.id);
                    }}
                    disabled={deleteConversationMutation.isPending}
                    className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                  >
                    <TrashIcon className="h-4 w-4" />
                    <span className="sr-only">Delete conversation</span>
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* New Conversation Modal */}
      {showNewConversationModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-background rounded-lg p-6 w-full max-w-md shadow-lg border">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-foreground">
                New Conversation
              </h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowNewConversationModal(false)}
              >
                ×
              </Button>
            </div>
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  Title *
                </label>
                <Input
                  value={newConversationTitle}
                  onChange={(e) => setNewConversationTitle(e.target.value)}
                  placeholder="Enter conversation title"
                  autoFocus
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  Description (optional)
                </label>
                <textarea
                  value={newConversationDescription}
                  onChange={(e) =>
                    setNewConversationDescription(e.target.value)
                  }
                  placeholder="Enter conversation description"
                  rows={3}
                  className="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 resize-none"
                />
              </div>
              <div className="flex flex-col-reverse sm:flex-row justify-end gap-3 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setShowNewConversationModal(false)}
                  className="w-full sm:w-auto"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateConversation}
                  disabled={createConversationMutation.isPending}
                  className="w-full sm:w-auto"
                >
                  {createConversationMutation.isPending
                    ? "Creating..."
                    : "Create Conversation"}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
