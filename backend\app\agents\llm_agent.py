"""
LLM-based agent implementation
"""

from typing import List, AsyncGenerator, Dict, Any
import asyncio
import time
from datetime import datetime
import os
import litellm
from loguru import logger

from app.agents.base_agent import BaseAgent, AgentMessage, AgentResponse, AgentContext, AgentCapability
from app.core.config import settings


class LLMAgent(BaseAgent):
    """LLM-based agent for text generation and conversation"""
    
    def __init__(self, agent):
        super().__init__(agent)
        self.model = agent.model or settings.DEFAULT_LLM_MODEL
        self.temperature = float(agent.temperature) if agent.temperature else 0.7
        self.max_tokens = agent.max_tokens or 2048
        
        # Set up LiteLLM configuration
        if settings.OPENAI_API_KEY:
            os.environ["OPENAI_API_KEY"] = settings.OPENAI_API_KEY
        if settings.ANTHROPIC_API_KEY:
            os.environ["ANTHROPIC_API_KEY"] = settings.ANTHROPIC_API_KEY
        if settings.OPENROUTER_API_KEY:
            os.environ["OPENROUTER_API_KEY"] = settings.OPENROUTER_API_KEY
    
    async def validate_input(self, messages: List[AgentMessage]) -> bool:
        """Validate input messages"""
        if not messages:
            return False
        
        # Check if last message is from user
        if messages[-1].role not in ["user", "system"]:
            return False
        
        return True
    
    async def execute(
        self,
        messages: List[AgentMessage],
        context: AgentContext
    ) -> AsyncGenerator[AgentResponse, None]:
        """Execute LLM agent"""
        start_time = time.time()
        
        try:
            # Validate input
            if not await self.validate_input(messages):
                yield await self.handle_error(
                    ValueError("Invalid input messages"),
                    context
                )
                return
            
            # Prepare messages for LLM
            llm_messages = await self.prepare_llm_messages(messages)
            
            # Add system prompt
            system_prompt = self.get_system_prompt()
            if system_prompt:
                llm_messages.insert(0, {
                    "role": "system",
                    "content": system_prompt
                })
            
            # Check if streaming is supported
            if self.get_config_value("streaming", True):
                async for response in self.stream_completion(llm_messages, context):
                    yield response
            else:
                response = await self.complete(llm_messages, context)
                yield response
            
            # Log execution
            execution_time = time.time() - start_time
            await self.log_execution(context, messages, response, execution_time)
            
        except Exception as e:
            yield await self.handle_error(e, context)
    
    async def prepare_llm_messages(self, messages: List[AgentMessage]) -> List[Dict[str, Any]]:
        """Prepare messages for LLM API"""
        llm_messages = []
        
        for msg in messages:
            llm_messages.append({
                "role": msg.role,
                "content": msg.content
            })
        
        return llm_messages
    
    async def complete(
        self,
        messages: List[Dict[str, Any]],
        context: AgentContext
    ) -> AgentResponse:
        """Get completion from LLM"""
        try:
            response = await litellm.acompletion(
                model=self.model,
                messages=messages,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                timeout=self.agent.max_execution_time
            )
            
            content = response.choices[0].message.content
            
            return AgentResponse(
                content=content,
                success=True,
                metadata={
                    "model": self.model,
                    "usage": response.usage.dict() if response.usage else None,
                    "finish_reason": response.choices[0].finish_reason
                }
            )
            
        except Exception as e:
            logger.error(f"LLM completion error: {e}")
            return AgentResponse(
                content="I'm sorry, I encountered an error while processing your request.",
                success=False,
                error=str(e)
            )
    
    async def stream_completion(
        self,
        messages: List[Dict[str, Any]],
        context: AgentContext
    ) -> AsyncGenerator[AgentResponse, None]:
        """Stream completion from LLM"""
        try:
            response_stream = await litellm.acompletion(
                model=self.model,
                messages=messages,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                stream=True,
                timeout=self.agent.max_execution_time
            )
            
            full_content = ""
            
            async for chunk in response_stream:
                if chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    full_content += content
                    
                    yield AgentResponse(
                        content=content,
                        success=True,
                        metadata={
                            "streaming": True,
                            "model": self.model,
                            "chunk": True
                        }
                    )
            
            # Send final response with complete content
            yield AgentResponse(
                content=full_content,
                success=True,
                metadata={
                    "streaming": True,
                    "model": self.model,
                    "complete": True,
                    "total_tokens": len(full_content.split())
                }
            )
            
        except Exception as e:
            logger.error(f"LLM streaming error: {e}")
            yield AgentResponse(
                content="I'm sorry, I encountered an error while processing your request.",
                success=False,
                error=str(e)
            )
    
    def get_system_prompt(self) -> str:
        """Get system prompt with agent-specific instructions"""
        base_prompt = super().get_system_prompt()
        
        # Add LLM-specific instructions
        llm_instructions = """
You are an AI assistant powered by a large language model. You should:
1. Provide helpful, accurate, and relevant responses
2. Be concise but thorough in your explanations
3. Ask clarifying questions when needed
4. Admit when you don't know something
5. Follow ethical guidelines and safety protocols
"""
        
        return f"{base_prompt}\n\n{llm_instructions}".strip()


class CodeAgent(LLMAgent):
    """Specialized agent for code generation and analysis"""
    
    def __init__(self, agent):
        super().__init__(agent)
        self.capabilities.extend([
            AgentCapability.CODE_EXECUTION.value,
            AgentCapability.FILE_PROCESSING.value
        ])
    
    def get_system_prompt(self) -> str:
        """Get system prompt for code agent"""
        base_prompt = super().get_system_prompt()
        
        code_instructions = """
You are a specialized coding assistant. You excel at:
1. Writing clean, efficient, and well-documented code
2. Debugging and fixing code issues
3. Code review and optimization suggestions
4. Explaining complex programming concepts
5. Working with multiple programming languages
6. Following best practices and design patterns

When providing code:
- Use proper syntax highlighting
- Include comments for complex logic
- Provide usage examples when appropriate
- Suggest improvements and alternatives
"""
        
        return f"{base_prompt}\n\n{code_instructions}".strip()


class ResearchAgent(LLMAgent):
    """Specialized agent for research and analysis"""
    
    def __init__(self, agent):
        super().__init__(agent)
        self.capabilities.extend([
            AgentCapability.WEB_BROWSING.value,
            AgentCapability.DATA_ANALYSIS.value,
            AgentCapability.FILE_PROCESSING.value
        ])
    
    def get_system_prompt(self) -> str:
        """Get system prompt for research agent"""
        base_prompt = super().get_system_prompt()
        
        research_instructions = """
You are a specialized research assistant. You excel at:
1. Conducting thorough research on various topics
2. Analyzing and synthesizing information from multiple sources
3. Fact-checking and verifying information
4. Creating comprehensive reports and summaries
5. Identifying trends and patterns in data
6. Providing citations and references

When conducting research:
- Verify information from multiple sources
- Provide clear citations and references
- Present balanced perspectives
- Highlight key findings and insights
- Structure information logically
"""
        
        return f"{base_prompt}\n\n{research_instructions}".strip()
