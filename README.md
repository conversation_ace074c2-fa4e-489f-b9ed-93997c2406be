# Agentico - AI Agent Platform

A next-generation AI agent platform that combines the strengths of leading solutions while introducing innovative features for enhanced productivity and adaptability.

## 🚀 Features

- **Multi-Agent Architecture**: Deploy specialized agents that work together
- **Visual Workflow Management**: Design and manage complex workflows with intuitive visual editor
- **Real-time Collaboration**: Live chat interface with WebSocket support
- **Enterprise Security**: Isolated execution environments, RBAC, and audit logging
- **Scalable Infrastructure**: Docker-based execution with auto-scaling capabilities
- **Modern Web Interface**: Responsive React/Next.js frontend with excellent UX/UI

## 🏗️ Architecture

### Backend
- **Framework**: FastAPI with Python 3.11+
- **Database**: PostgreSQL with SQLAlchemy 2.0
- **Real-time**: Socket.io for WebSocket communication
- **Task Queue**: Celery with Redis
- **Authentication**: JWT with OAuth2
- **File Storage**: MinIO (S3-compatible)
- **Containerization**: Docker with Docker Compose

### Frontend
- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS + Shadcn/ui components
- **State Management**: Zustand + React Query
- **Real-time**: Socket.io client
- **Authentication**: NextAuth.js
- **Testing**: Vitest + React Testing Library + Playwright

## 🛠️ Quick Start

### Prerequisites
- Docker and Docker Compose
- Node.js 18+ (for local development)
- Python 3.11+ (for local development)

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd agentico
   ```

2. **Start the development environment**
   ```bash
   docker-compose up -d
   ```

3. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs
   - MinIO Console: http://localhost:9001

### Environment Variables

Create `.env` files in the backend and frontend directories:

**Backend (.env)**
```env
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/agentico
REDIS_URL=redis://localhost:6379
JWT_SECRET_KEY=your-super-secret-jwt-key
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
```

**Frontend (.env.local)**
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret
```

## 📁 Project Structure

```
agentico/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core configuration
│   │   ├── models/         # Database models
│   │   ├── services/       # Business logic
│   │   ├── tasks/          # Celery tasks
│   │   └── main.py         # FastAPI app
│   ├── tests/              # Backend tests
│   ├── alembic/            # Database migrations
│   └── Dockerfile
├── frontend/               # Next.js frontend
│   ├── src/
│   │   ├── app/            # Next.js app router
│   │   ├── components/     # React components
│   │   ├── hooks/          # Custom hooks
│   │   ├── lib/            # Utilities
│   │   ├── services/       # API services
│   │   ├── stores/         # Zustand stores
│   │   └── types/          # TypeScript types
│   ├── public/             # Static assets
│   └── Dockerfile
├── docker-compose.yml      # Development environment
└── README.md
```

## 🧪 Testing

### Backend Tests
```bash
cd backend
pytest
```

### Frontend Tests
```bash
cd frontend
npm test
```

### End-to-End Tests
```bash
cd frontend
npm run e2e
```

## 🚀 Deployment

### Production Build
```bash
docker-compose -f docker-compose.prod.yml up -d
```

### Environment-specific Configurations
- Development: `docker-compose.yml`
- Staging: `docker-compose.staging.yml`
- Production: `docker-compose.prod.yml`

## 📚 API Documentation

The API documentation is automatically generated and available at:
- Development: http://localhost:8000/docs
- Interactive API explorer with request/response examples

## 🔧 Development

### Backend Development
```bash
cd backend
poetry install
poetry shell
uvicorn app.main:app --reload
```

### Frontend Development
```bash
cd frontend
npm install
npm run dev
```

### Database Migrations
```bash
cd backend
alembic revision --autogenerate -m "Description"
alembic upgrade head
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- Documentation: [docs.agentico.com](https://docs.agentico.com)
- Issues: [GitHub Issues](https://github.com/agentico/agentico/issues)
- Discussions: [GitHub Discussions](https://github.com/agentico/agentico/discussions)

## 🗺️ Roadmap

### Phase 1: Foundation (0-3 months) ✅
- [x] Core architecture setup
- [x] Basic agent framework
- [x] Authentication system
- [x] Real-time communication
- [x] File management

### Phase 2: Expansion (3-6 months)
- [ ] Specialized agents
- [ ] Visual workflow system
- [ ] Enterprise features
- [ ] API ecosystem

### Phase 3: Optimization (6-12 months)
- [ ] Performance tuning
- [ ] Advanced analytics
- [ ] AI model optimization
- [ ] Ecosystem integration
