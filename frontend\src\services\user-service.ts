import { apiClient } from '@/lib/api-client';

export interface User {
  id: number;
  email: string;
  full_name: string;
  bio?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface UserSettings {
  email_notifications: boolean;
  push_notifications: boolean;
  weekly_digest: boolean;
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
}

export interface UpdateProfileData {
  full_name?: string;
  email?: string;
  bio?: string;
}

export interface ChangePasswordData {
  current_password: string;
  new_password: string;
}

class UserService {
  async getCurrentUser(): Promise<User> {
    const response = await apiClient.get('/auth/me');
    return response.data;
  }

  async updateProfile(data: UpdateProfileData): Promise<User> {
    const response = await apiClient.put('/auth/profile', data);
    return response.data;
  }

  async changePassword(data: ChangePasswordData): Promise<void> {
    await apiClient.post('/auth/change-password', data);
  }

  async getUserSettings(): Promise<UserSettings> {
    const response = await apiClient.get('/auth/settings');
    return response.data;
  }

  async updateSettings(data: UserSettings): Promise<UserSettings> {
    const response = await apiClient.put('/auth/settings', data);
    return response.data;
  }

  async deleteAccount(): Promise<void> {
    await apiClient.delete('/auth/account');
  }
}

export const userService = new UserService();