export interface Conversation {
  id: number;
  title: string;
  description?: string;
  status: string;
  is_archived: boolean;
  context?: Record<string, any>;
  metadata?: Record<string, any>;
  message_count: number;
  total_tokens: number;
  created_at: string;
  updated_at: string;
  last_message_at?: string;
  user_id: number;
  agent_id?: number;
}

export interface ConversationCreate {
  title: string;
  description?: string;
  agent_id?: number;
  context?: Record<string, any>;
}

export interface ConversationUpdate {
  title?: string;
  description?: string;
  status?: string;
  context?: Record<string, any>;
}

export interface Message {
  id: number;
  role: string;
  content: string;
  metadata?: Record<string, any>;
  tokens: number;
  tool_calls?: Array<Record<string, any>>;
  tool_results?: Array<Record<string, any>>;
  created_at: string;
  updated_at: string;
  conversation_id: number;
}

export interface MessageCreate {
  content: string;
  role?: string;
  metadata?: Record<string, any>;
}
