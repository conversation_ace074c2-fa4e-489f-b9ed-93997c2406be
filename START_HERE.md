# 🚀 Start Here - Agentico Setup

Welcome to **Agentico** - Your AI Agent Platform! Follow these simple steps to get started.

## 📋 Before You Begin

You need **Docker Desktop** installed and running on your computer.

### 🔽 Install Docker Desktop

**Windows:**
1. Download from: https://www.docker.com/products/docker-desktop/
2. Run the installer
3. Restart your computer
4. **Start Docker Desktop** from the Start Menu
5. Wait for the whale icon in your system tray to show "Docker Desktop is running"

**Mac:**
1. Download from: https://www.docker.com/products/docker-desktop/
2. Drag Docker to Applications
3. **Launch Docker Desktop**
4. Wait for the whale icon in your menu bar

**Linux:**
```bash
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
# Log out and back in
```

## 🚀 Quick Start (2 Minutes)

### Step 1: Download Agentico
```bash
git clone <repository-url>
cd agentico
```

### Step 2: Deploy
**Windows:** Double-click `deploy.bat`
**Mac/Linux:** Run `./deploy.sh`

### Step 3: Access
Visit: http://localhost:3000

That's it! 🎉

## 🔧 Manual Setup (If Needed)

If the automated script doesn't work:

1. **Create environment file:**
   ```bash
   copy .env.example .env    # Windows
   cp .env.example .env      # Mac/Linux
   ```

2. **Start services:**
   ```bash
   docker-compose up -d
   ```

3. **Wait 2-3 minutes** for all services to start

4. **Test deployment:**
   ```bash
   python test-deployment.py
   ```

## 🌐 Access Your Platform

Once running, you can access:

- **🎯 Main App**: http://localhost:3000
- **📚 API Docs**: http://localhost:8000/docs
- **🗄️ File Storage**: Local file system (managed via API)

## 👤 First Steps

1. **Create Account**: Visit http://localhost:3000 and sign up
2. **Create Agent**: Go to Dashboard → Agents → Create Agent
3. **Start Chat**: Go to Dashboard → Conversations → Start Conversation
4. **Have Fun**: Chat with your AI agent!

## 🔑 Optional: Add AI API Keys

For full AI functionality, add your API keys to `.env`:

```bash
OPENAI_API_KEY=your-openai-key-here
ANTHROPIC_API_KEY=your-anthropic-key-here
```

Get keys from:
- OpenAI: https://platform.openai.com/api-keys
- Anthropic: https://console.anthropic.com/

## ❓ Need Help?

### Common Issues

**"Docker not running"**
- Start Docker Desktop and wait for it to be ready

**"Port already in use"**
- Stop other applications using ports 3000, 8000, 5432, 6379, 9000

**"Services won't start"**
- Run: `docker-compose logs` to see what's wrong
- Try: `docker-compose down && docker-compose up -d`

### Get Support

1. Check the logs: `docker-compose logs`
2. Read the full guide: `DEPLOYMENT_GUIDE.md`
3. Run tests: `python test-deployment.py`

## 🎉 You're Ready!

Once everything is running:

✅ **Create your first AI agent**
✅ **Start conversations**
✅ **Upload and process files**
✅ **Build workflows**
✅ **Explore the API**

Welcome to the future of AI agents! 🤖
