'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import {
  ArrowLeftIcon,
  ChatBubbleLeftRightIcon,
  CpuChipIcon,
} from '@heroicons/react/24/outline';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { conversationService } from '@/services/conversation-service';
import { agentService } from '@/services/agent-service';
import type { Agent } from '@/types/agent';

export default function NewConversationPage() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    agent_id: undefined as number | undefined,
  });

  // Fetch agents for selection
  const { data: agents, isLoading: agentsLoading } = useQuery({
    queryKey: ['agents'],
    queryFn: () => agentService.getAgents(),
  });

  // Create conversation mutation
  const createConversationMutation = useMutation({
    mutationFn: conversationService.createConversation,
    onSuccess: (conversation) => {
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
      toast.success('Conversation created successfully');
      // Navigate to the conversation chat interface
      router.push(`/dashboard/conversations/${conversation.id}`);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create conversation');
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      toast.error('Please enter a conversation title');
      return;
    }

    createConversationMutation.mutate({
      title: formData.title,
      description: formData.description || undefined,
      agent_id: formData.agent_id,
    });
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeftIcon className="h-4 w-4" />
          Back
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Start New Conversation</h1>
          <p className="mt-1 text-sm text-gray-500">
            Create a new conversation with an AI agent
          </p>
        </div>
      </div>

      {/* Form */}
      <Card className="p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Conversation Details</h3>
            
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                Conversation Title *
              </label>
              <Input
                id="title"
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="Enter conversation title"
                required
              />
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Description (Optional)
              </label>
              <textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Describe the purpose of this conversation"
                rows={3}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* Agent Selection */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Select Agent (Optional)</h3>
            <p className="text-sm text-gray-500">
              Choose an AI agent to chat with, or leave blank to select one later.
            </p>
            
            {agentsLoading ? (
              <div className="flex items-center justify-center py-8">
                <LoadingSpinner size="sm" />
                <span className="ml-2 text-sm text-gray-500">Loading agents...</span>
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
                <div
                  className={`relative cursor-pointer rounded-lg border-2 p-4 transition-colors ${
                    formData.agent_id === undefined
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleInputChange('agent_id', undefined)}
                >
                  <div className="flex items-center">
                    <ChatBubbleLeftRightIcon className="h-6 w-6 text-gray-400 mr-3" />
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">No Agent</h4>
                      <p className="text-xs text-gray-500">Start without an agent</p>
                    </div>
                  </div>
                </div>
                
                {agents?.map((agent: Agent) => (
                  <div
                    key={agent.id}
                    className={`relative cursor-pointer rounded-lg border-2 p-4 transition-colors ${
                      formData.agent_id === agent.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleInputChange('agent_id', agent.id)}
                  >
                    <div className="flex items-start">
                      <CpuChipIcon className="h-6 w-6 text-blue-500 mr-3 mt-0.5" />
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-gray-900 truncate">
                          {agent.name}
                        </h4>
                        <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                          {agent.description || 'No description available'}
                        </p>
                        <div className="mt-2">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
                            {agent.agent_type}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end gap-4 pt-6 border-t border-gray-200">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={createConversationMutation.isPending}
              className="flex items-center gap-2"
            >
              {createConversationMutation.isPending ? (
                <LoadingSpinner size="sm" />
              ) : (
                <ChatBubbleLeftRightIcon className="h-4 w-4" />
              )}
              Start Conversation
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
}