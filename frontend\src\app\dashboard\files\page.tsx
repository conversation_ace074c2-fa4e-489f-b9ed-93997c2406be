'use client';

import React, { useState, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useDropzone } from 'react-dropzone';
import { toast } from 'sonner';
import {
  PlusIcon,
  DocumentIcon,
  CloudArrowUpIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  TrashIcon,
  ArrowDownTrayIcon,
  ShareIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { fileService } from '@/services/file-service';
import type { FileItem, FileUploadProgress, FileListParams } from '@/types/file';

export default function FilesPage() {
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFiles, setSelectedFiles] = useState<number[]>([]);
  const [uploadProgress, setUploadProgress] = useState<FileUploadProgress[]>([]);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [filters, setFilters] = useState<FileListParams>({
    skip: 0,
    limit: 20,
  });

  // Fetch files
  const { data: files = [], isLoading, error } = useQuery({
    queryKey: ['files', filters],
    queryFn: () => fileService.getFiles(filters),
  });

  // Fetch file stats
  const { data: stats } = useQuery({
    queryKey: ['file-stats'],
    queryFn: () => fileService.getFileStats(),
  });

  // Upload mutation
  const uploadMutation = useMutation({
    mutationFn: async (file: File) => {
      const uploadId = Date.now() + Math.random();
      const progressItem: FileUploadProgress = {
        file,
        progress: 0,
        status: 'uploading',
      };

      setUploadProgress(prev => [...prev, progressItem]);

      try {
        const result = await fileService.uploadFile(file, (progress) => {
          setUploadProgress(prev =>
            prev.map(item =>
              item.file === file
                ? { ...item, progress }
                : item
            )
          );
        });

        setUploadProgress(prev =>
          prev.map(item =>
            item.file === file
              ? { ...item, status: 'completed', result }
              : item
          )
        );

        return result;
      } catch (error) {
        setUploadProgress(prev =>
          prev.map(item =>
            item.file === file
              ? { ...item, status: 'error', error: 'Upload failed' }
              : item
          )
        );
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['files'] });
      queryClient.invalidateQueries({ queryKey: ['file-stats'] });
      toast.success('File uploaded successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Upload failed');
    },
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: fileService.deleteFile,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['files'] });
      queryClient.invalidateQueries({ queryKey: ['file-stats'] });
      toast.success('File deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Delete failed');
    },
  });

  // Dropzone configuration
  const onDrop = useCallback((acceptedFiles: File[]) => {
    acceptedFiles.forEach(file => {
      uploadMutation.mutate(file);
    });
    setShowUploadModal(false);
  }, [uploadMutation]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: true,
    maxSize: 100 * 1024 * 1024, // 100MB
  });

  const handleDownload = async (file: FileItem) => {
    try {
      const blob = await fileService.downloadFile(file.storage_path);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = file.original_filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('Download started');
    } catch (error) {
      toast.error('Download failed');
    }
  };

  const handleDelete = (fileId: number) => {
    if (confirm('Are you sure you want to delete this file?')) {
      deleteMutation.mutate(fileId);
    }
  };

  const clearUploadProgress = () => {
    setUploadProgress([]);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="space-y-1">
          <h1 className="text-2xl font-bold text-gray-900">Files</h1>
          <p className="text-gray-600">Upload and manage your files for AI processing</p>
        </div>
        <Button
          onClick={() => setShowUploadModal(true)}
          className="flex items-center gap-2 w-full sm:w-auto"
        >
          <PlusIcon className="h-4 w-4" />
          Upload Files
        </Button>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="p-4">
            <div className="text-sm text-gray-600">Total Files</div>
            <div className="text-2xl font-bold text-gray-900">{stats.total_count}</div>
          </Card>
          <Card className="p-4">
            <div className="text-sm text-gray-600">Storage Used</div>
            <div className="text-2xl font-bold text-gray-900">{stats.total_size_mb} MB</div>
          </Card>
          <Card className="p-4">
            <div className="text-sm text-gray-600">Ready</div>
            <div className="text-2xl font-bold text-green-600">{stats.ready_count}</div>
          </Card>
          <Card className="p-4">
            <div className="text-sm text-gray-600">Processing</div>
            <div className="text-2xl font-bold text-yellow-600">{stats.processing_count}</div>
          </Card>
        </div>
      )}

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
        <div className="relative flex-1 max-w-md">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search files..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button variant="outline" className="flex items-center gap-2 w-full sm:w-auto">
          <FunnelIcon className="h-4 w-4" />
          Filters
        </Button>
      </div>

      {/* Upload Progress */}
      {uploadProgress.length > 0 && (
        <Card className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium">Upload Progress</h3>
            <Button
              variant="outline"
              size="sm"
              onClick={clearUploadProgress}
              className="flex items-center gap-2"
            >
              <XMarkIcon className="h-4 w-4" />
              Clear
            </Button>
          </div>
          <div className="space-y-3">
            {uploadProgress.map((item, index) => (
              <div key={index} className="flex items-center gap-3">
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium">{item.file.name}</span>
                    <span className="text-sm text-gray-500">
                      {item.status === 'uploading' && `${item.progress}%`}
                      {item.status === 'completed' && '✓ Complete'}
                      {item.status === 'error' && '✗ Error'}
                    </span>
                  </div>
                  {item.status === 'uploading' && (
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${item.progress}%` }}
                      />
                    </div>
                  )}
                  {item.status === 'error' && (
                    <div className="text-sm text-red-600">{item.error}</div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Files List */}
      {files.length === 0 ? (
        <Card className="p-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center space-y-4">
              <DocumentIcon className="h-12 w-12 mx-auto text-muted-foreground" />
              <div className="space-y-2">
                <h3 className="text-lg font-medium text-foreground">
                  {searchQuery ? 'No files found' : 'No files uploaded yet'}
                </h3>
                <p className="text-sm text-muted-foreground max-w-sm mx-auto">
                  {searchQuery
                    ? 'Try adjusting your search terms'
                    : 'Upload your first file to get started with AI processing.'
                  }
                </p>
              </div>
              <Button
                onClick={() => setShowUploadModal(true)}
                variant="outline"
                className="flex items-center gap-2"
              >
                <CloudArrowUpIcon className="h-4 w-4" />
                Upload Files
              </Button>
            </div>
          </div>
        </Card>
      ) : (
        <div className="grid gap-4">
          {files.map((file) => (
            <Card key={file.id} className="p-4 hover:shadow-md transition-all duration-200 hover:border-primary/20">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  <div className="text-2xl flex-shrink-0">
                    {fileService.getFileIcon(file.file_type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-foreground truncate">{file.original_filename}</h3>
                    <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                      <span>{fileService.formatFileSize(file.file_size)}</span>
                      <span>•</span>
                      <span className={`px-2 py-1 rounded-full text-xs w-fit ${fileService.getStatusColor(file.status)}`}>
                        {file.status}
                      </span>
                      <span>•</span>
                      <span>{new Date(file.created_at).toLocaleDateString()}</span>
                      {file.download_count > 0 && (
                        <>
                          <span>•</span>
                          <span>{file.download_count} downloads</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2 flex-shrink-0">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDownload(file)}
                    className="h-8 w-8 p-0"
                  >
                    <ArrowDownTrayIcon className="h-4 w-4" />
                    <span className="sr-only">Download file</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {}}
                    className="h-8 w-8 p-0"
                  >
                    <EyeIcon className="h-4 w-4" />
                    <span className="sr-only">Preview file</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {}}
                    className="h-8 w-8 p-0"
                  >
                    <ShareIcon className="h-4 w-4" />
                    <span className="sr-only">Share file</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(file.id)}
                    className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                  >
                    <TrashIcon className="h-4 w-4" />
                    <span className="sr-only">Delete file</span>
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Upload Modal */}
      {showUploadModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-background rounded-lg p-6 w-full max-w-md shadow-lg border">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-foreground">Upload Files</h2>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowUploadModal(false)}
                className="h-8 w-8 p-0"
              >
                <XMarkIcon className="h-4 w-4" />
                <span className="sr-only">Close modal</span>
              </Button>
            </div>
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                isDragActive ? 'border-primary bg-primary/5' : 'border-border hover:border-primary/50'
              }`}
            >
              <input {...getInputProps()} />
              <CloudArrowUpIcon className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <div className="space-y-2">
                <p className="text-lg font-medium text-foreground">
                  {isDragActive ? 'Drop files here' : 'Drag & drop files here'}
                </p>
                <p className="text-sm text-muted-foreground">
                  or click to browse files
                </p>
                <p className="text-xs text-muted-foreground">
                  Maximum file size: 100MB
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}