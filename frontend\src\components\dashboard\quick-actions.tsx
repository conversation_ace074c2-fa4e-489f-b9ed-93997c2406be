'use client';

import Link from 'next/link';
import {
  PlusIcon,
  CpuChipIcon,
  ChatBubbleLeftRightIcon,
  DocumentArrowUpIcon,
} from '@heroicons/react/24/outline';

const actions = [
  {
    name: 'Chat with <PERSON>',
    description: 'Start a conversation with your AI assistant',
    href: '/dashboard/chat',
    icon: ChatBubbleLeftRightIcon,
    color: 'bg-blue-500 hover:bg-blue-600',
  },
  {
    name: 'View Conversations',
    description: 'Browse your chat history',
    href: '/dashboard/conversations',
    icon: ChatBubbleLeftRightIcon,
    color: 'bg-green-500 hover:bg-green-600',
  },
  {
    name: 'Upload Files',
    description: 'Add files for AI processing',
    href: '/dashboard/files/upload',
    icon: DocumentArrowUpIcon,
    color: 'bg-purple-500 hover:bg-purple-600',
  },
];

export function QuickActions() {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
        <PlusIcon className="h-5 w-5 text-gray-400" />
      </div>
      
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
        {actions.map((action) => (
          <Link
            key={action.name}
            href={action.href}
            className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
          >
            <div>
              <span className={`rounded-lg inline-flex p-3 text-white ${action.color} transition-colors`}>
                <action.icon className="h-6 w-6" />
              </span>
            </div>
            <div className="mt-4">
              <h3 className="text-lg font-medium text-gray-900">
                <span className="absolute inset-0" aria-hidden="true" />
                {action.name}
              </h3>
              <p className="mt-2 text-sm text-gray-500">
                {action.description}
              </p>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
}
