"""
File model
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, JSON, ForeignKey, Enum, BigInteger
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from app.core.database import Base


class FileStatus(str, enum.Enum):
    """File status enumeration"""
    UPLOADING = "uploading"
    PROCESSING = "processing"
    READY = "ready"
    ERROR = "error"
    DELETED = "deleted"


class FileType(str, enum.Enum):
    """File type enumeration"""
    DOCUMENT = "document"
    IMAGE = "image"
    AUDIO = "audio"
    VIDEO = "video"
    CODE = "code"
    DATA = "data"
    ARCHIVE = "archive"
    OTHER = "other"


class File(Base):
    """File model"""
    
    __tablename__ = "files"
    
    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    
    # File properties
    file_type = Column(Enum(FileType), nullable=False)
    mime_type = Column(String(100), nullable=False)
    file_size = Column(BigInteger, nullable=False)  # Size in bytes
    file_hash = Column(String(64), nullable=False, index=True)  # SHA-256 hash
    
    # Storage information
    storage_path = Column(String(500), nullable=False)  # Path in storage
    bucket_name = Column(String(100), nullable=False)
    storage_provider = Column(String(50), default="minio", nullable=False)
    
    # Status and processing
    status = Column(Enum(FileStatus), default=FileStatus.UPLOADING, nullable=False)
    is_public = Column(Boolean, default=False, nullable=False)
    is_temporary = Column(Boolean, default=False, nullable=False)
    
    # Content analysis
    content_type = Column(String(100), nullable=True)
    extracted_text = Column(Text, nullable=True)  # Extracted text content
    meta_data = Column(JSON, default=dict, nullable=True)  # File metadata
    
    # Processing information
    processing_status = Column(String(50), nullable=True)
    processing_error = Column(Text, nullable=True)
    processing_progress = Column(Integer, default=0, nullable=False)
    
    # Access control
    access_level = Column(String(20), default="private", nullable=False)  # private, shared, public
    shared_with = Column(JSON, default=list, nullable=True)  # List of user IDs
    
    # Usage tracking
    download_count = Column(Integer, default=0, nullable=False)
    view_count = Column(Integer, default=0, nullable=False)
    
    # Expiration
    expires_at = Column(DateTime(timezone=True), nullable=True)
    auto_delete = Column(Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    last_accessed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Foreign keys
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Relationships
    owner = relationship("User", back_populates="files")
    
    def __repr__(self):
        return f"<File(id={self.id}, filename='{self.filename}', type='{self.file_type}', status='{self.status}')>"
    
    @property
    def file_size_mb(self) -> float:
        """Get file size in MB"""
        return round(self.file_size / (1024 * 1024), 2)
    
    @property
    def file_extension(self) -> str:
        """Get file extension"""
        return self.original_filename.split('.')[-1].lower() if '.' in self.original_filename else ''
    
    @property
    def is_expired(self) -> bool:
        """Check if file is expired"""
        if not self.expires_at:
            return False
        return self.expires_at < func.now()
    
    @property
    def is_accessible(self) -> bool:
        """Check if file is accessible"""
        return (
            self.status == FileStatus.READY and
            not self.is_expired and
            self.status != FileStatus.DELETED
        )
    
    def increment_download_count(self):
        """Increment download count"""
        self.download_count += 1
        self.last_accessed_at = func.now()
    
    def increment_view_count(self):
        """Increment view count"""
        self.view_count += 1
        self.last_accessed_at = func.now()
    
    def share_with_user(self, user_id: int):
        """Share file with a user"""
        if not self.shared_with:
            self.shared_with = []
        if user_id not in self.shared_with:
            self.shared_with.append(user_id)
    
    def unshare_with_user(self, user_id: int):
        """Unshare file with a user"""
        if self.shared_with and user_id in self.shared_with:
            self.shared_with.remove(user_id)
    
    def is_shared_with_user(self, user_id: int) -> bool:
        """Check if file is shared with a user"""
        return user_id in (self.shared_with or [])
    
    def can_access(self, user_id: int) -> bool:
        """Check if user can access the file"""
        if not self.is_accessible:
            return False
        
        # Owner can always access
        if self.owner_id == user_id:
            return True
        
        # Public files can be accessed by anyone
        if self.is_public:
            return True
        
        # Check if shared with user
        if self.access_level == "shared" and self.is_shared_with_user(user_id):
            return True
        
        return False
    
    def to_dict(self) -> dict:
        """Convert file to dictionary"""
        return {
            "id": self.id,
            "filename": self.filename,
            "original_filename": self.original_filename,
            "file_type": self.file_type.value if self.file_type else None,
            "mime_type": self.mime_type,
            "file_size": self.file_size,
            "file_size_mb": self.file_size_mb,
            "file_extension": self.file_extension,
            "file_hash": self.file_hash,
            "storage_path": self.storage_path,
            "bucket_name": self.bucket_name,
            "storage_provider": self.storage_provider,
            "status": self.status.value if self.status else None,
            "is_public": self.is_public,
            "is_temporary": self.is_temporary,
            "content_type": self.content_type,
            "extracted_text": self.extracted_text,
            "metadata": self.meta_data,
            "processing_status": self.processing_status,
            "processing_error": self.processing_error,
            "processing_progress": self.processing_progress,
            "access_level": self.access_level,
            "shared_with": self.shared_with,
            "download_count": self.download_count,
            "view_count": self.view_count,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "auto_delete": self.auto_delete,
            "is_expired": self.is_expired,
            "is_accessible": self.is_accessible,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_accessed_at": self.last_accessed_at.isoformat() if self.last_accessed_at else None,
            "owner_id": self.owner_id,
        }
