"""
User service for database operations
"""

from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.sql import func
from datetime import datetime

from app.models.user import User
from app.core.exceptions import NotFoundError, ValidationError


class UserService:
    """User service for database operations"""

    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create(self, user_data: Dict[str, Any]) -> User:
        """Create a new user"""
        user = User(**user_data)
        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)
        return user

    async def get_by_id(self, user_id: int) -> Optional[User]:
        """Get user by ID"""
        result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        return result.scalar_one_or_none()

    async def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email"""
        result = await self.db.execute(
            select(User).where(User.email == email)
        )
        return result.scalar_one_or_none()

    async def get_by_username(self, username: str) -> Optional[User]:
        """Get user by username"""
        result = await self.db.execute(
            select(User).where(User.username == username)
        )
        return result.scalar_one_or_none()
    
    async def get_all(
        self,
        skip: int = 0,
        limit: int = 100,
        is_active: Optional[bool] = None
    ) -> List[User]:
        """Get all users with pagination"""
        query = select(User)
        
        if is_active is not None:
            query = query.where(User.is_active == is_active)
        
        query = query.offset(skip).limit(limit).order_by(User.created_at.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def update(self, user_id: int, user_data: Dict[str, Any]) -> User:
        """Update user"""
        user = await self.get_by_id(user_id)
        if not user:
            raise NotFoundError(f"User with ID {user_id} not found")
        
        # Update fields
        for field, value in user_data.items():
            if hasattr(user, field):
                setattr(user, field, value)
        
        user.updated_at = func.now()
        await self.db.commit()
        await self.db.refresh(user)
        return user
    
    async def delete(self, user_id: int) -> bool:
        """Delete user"""
        user = await self.get_by_id(user_id)
        if not user:
            raise NotFoundError(f"User with ID {user_id} not found")
        
        await self.db.delete(user)
        await self.db.commit()
        return True
    
    async def update_last_login(self, user_id: int) -> User:
        """Update user's last login timestamp"""
        return await self.update(user_id, {"last_login_at": func.now()})
    
    async def activate_user(self, user_id: int) -> User:
        """Activate user account"""
        return await self.update(user_id, {"is_active": True})
    
    async def deactivate_user(self, user_id: int) -> User:
        """Deactivate user account"""
        return await self.update(user_id, {"is_active": False})
    
    async def verify_email(self, user_id: int) -> User:
        """Mark user email as verified"""
        return await self.update(user_id, {
            "is_verified": True,
            "email_verified_at": func.now()
        })
    
    async def count_users(self, is_active: Optional[bool] = None) -> int:
        """Count total users"""
        query = select(func.count(User.id))
        
        if is_active is not None:
            query = query.where(User.is_active == is_active)
        
        result = await self.db.execute(query)
        return result.scalar()
    
    async def search_users(
        self,
        search_term: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[User]:
        """Search users by email, username, or full name"""
        search_pattern = f"%{search_term}%"
        
        query = select(User).where(
            (User.email.ilike(search_pattern)) |
            (User.username.ilike(search_pattern)) |
            (User.full_name.ilike(search_pattern))
        ).offset(skip).limit(limit).order_by(User.created_at.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_user_stats(self, user_id: int) -> Dict[str, Any]:
        """Get user statistics"""
        user = await self.get_by_id(user_id)
        if not user:
            raise NotFoundError(f"User with ID {user_id} not found")
        
        # Get related counts
        from app.models.agent import Agent
        from app.models.conversation import Conversation
        from app.models.task import Task
        from app.models.file import File
        
        agents_count = await self.db.scalar(
            select(func.count(Agent.id)).where(Agent.owner_id == user_id)
        )
        
        conversations_count = await self.db.scalar(
            select(func.count(Conversation.id)).where(Conversation.user_id == user_id)
        )
        
        tasks_count = await self.db.scalar(
            select(func.count(Task.id)).where(Task.user_id == user_id)
        )
        
        files_count = await self.db.scalar(
            select(func.count(File.id)).where(File.owner_id == user_id)
        )
        
        return {
            "user": user.to_dict(),
            "stats": {
                "agents_count": agents_count or 0,
                "conversations_count": conversations_count or 0,
                "tasks_count": tasks_count or 0,
                "files_count": files_count or 0,
                "account_age_days": (datetime.utcnow() - user.created_at).days if user.created_at else 0
            }
        }
    
    async def get_user_settings(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user settings"""
        from app.models.user_settings import UserSettings
        
        result = await self.db.execute(
            select(UserSettings).where(UserSettings.user_id == user_id)
        )
        settings = result.scalar_one_or_none()
        
        if settings:
            return settings.to_dict()
        return None
    
    async def update_user_settings(self, user_id: int, settings_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update or create user settings"""
        from app.models.user_settings import UserSettings
        
        # Check if settings exist
        result = await self.db.execute(
            select(UserSettings).where(UserSettings.user_id == user_id)
        )
        settings = result.scalar_one_or_none()
        
        if settings:
            # Update existing settings
            for field, value in settings_data.items():
                if hasattr(settings, field):
                    setattr(settings, field, value)
            settings.updated_at = func.now()
        else:
            # Create new settings
            settings = UserSettings(user_id=user_id, **settings_data)
            self.db.add(settings)
        
        await self.db.commit()
        await self.db.refresh(settings)
        return settings.to_dict()
