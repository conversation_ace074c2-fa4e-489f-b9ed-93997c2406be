"""
Agent model
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, JSON, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from app.core.database import Base


class AgentStatus(str, enum.Enum):
    """Agent status enumeration"""
    IDLE = "IDLE"
    RUNNING = "RUNNING"
    PAUSED = "PAUSED"
    ERROR = "ERROR"
    STOPPED = "STOPPED"


class AgentType(str, enum.Enum):
    """Agent type enumeration"""
    GENERAL = "general"
    CODE = "code"
    RESEARCH = "research"
    CREATIVE = "creative"
    ANALYSIS = "analysis"
    AUTOMATION = "automation"
    CUSTOM = "custom"
    SUPER = "super"


class Agent(Base):
    """Agent model"""
    
    __tablename__ = "agents"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # Agent configuration
    agent_type = Column(Enum(AgentType, values_callable=lambda obj: [e.value for e in obj]), default=AgentType.SUPER, nullable=False)
    model = Column(String(100), nullable=False)  # LLM model to use
    system_prompt = Column(Text, nullable=True)
    temperature = Column(String(10), default="0.7", nullable=False)
    max_tokens = Column(Integer, default=2048, nullable=False)
    
    # Status and execution
    status = Column(Enum(AgentStatus, values_callable=lambda obj: [e.value for e in obj]), default=AgentStatus.IDLE, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_public = Column(Boolean, default=False, nullable=False)
    
    # Capabilities and tools
    capabilities = Column(JSON, default=list, nullable=True)  # List of capabilities
    tools = Column(JSON, default=list, nullable=True)  # Available tools
    docker_image = Column(String(255), nullable=True)  # Custom Docker image
    
    # Configuration
    config = Column(JSON, default=dict, nullable=True)  # Agent-specific config
    environment_vars = Column(JSON, default=dict, nullable=True)  # Environment variables
    
    # Execution limits
    max_execution_time = Column(Integer, default=300, nullable=False)  # seconds
    max_memory_mb = Column(Integer, default=512, nullable=False)
    max_cpu_percent = Column(Integer, default=50, nullable=False)
    
    # Statistics
    total_executions = Column(Integer, default=0, nullable=False)
    successful_executions = Column(Integer, default=0, nullable=False)
    failed_executions = Column(Integer, default=0, nullable=False)
    average_execution_time = Column(String(20), nullable=True)  # in seconds
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    last_executed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Foreign keys
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Relationships
    owner = relationship("User", back_populates="agents")
    tasks = relationship("Task", back_populates="agent", cascade="all, delete-orphan")
    conversations = relationship("Conversation", back_populates="agent")
    
    def __repr__(self):
        return f"<Agent(id={self.id}, name='{self.name}', type='{self.agent_type}', status='{self.status}')>"
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate"""
        if self.total_executions == 0:
            return 0.0
        return (self.successful_executions / self.total_executions) * 100
    
    def increment_execution_stats(self, success: bool, execution_time: float):
        """Update execution statistics"""
        self.total_executions += 1
        if success:
            self.successful_executions += 1
        else:
            self.failed_executions += 1
        
        # Update average execution time
        if self.average_execution_time:
            current_avg = float(self.average_execution_time)
            new_avg = ((current_avg * (self.total_executions - 1)) + execution_time) / self.total_executions
            self.average_execution_time = str(round(new_avg, 2))
        else:
            self.average_execution_time = str(round(execution_time, 2))
    
    def add_capability(self, capability: str):
        """Add capability to agent"""
        if not self.capabilities:
            self.capabilities = []
        if capability not in self.capabilities:
            self.capabilities.append(capability)
    
    def remove_capability(self, capability: str):
        """Remove capability from agent"""
        if self.capabilities and capability in self.capabilities:
            self.capabilities.remove(capability)
    
    def add_tool(self, tool: dict):
        """Add tool to agent"""
        if not self.tools:
            self.tools = []
        # Check if tool already exists (by name)
        tool_names = [t.get("name") for t in self.tools]
        if tool.get("name") not in tool_names:
            self.tools.append(tool)
    
    def remove_tool(self, tool_name: str):
        """Remove tool from agent"""
        if self.tools:
            self.tools = [t for t in self.tools if t.get("name") != tool_name]
    
    def to_dict(self) -> dict:
        """Convert agent to dictionary"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "agent_type": self.agent_type.value if self.agent_type else None,
            "model": self.model,
            "system_prompt": self.system_prompt,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "status": self.status.value if self.status else None,
            "is_active": self.is_active,
            "is_public": self.is_public,
            "capabilities": self.capabilities,
            "tools": self.tools,
            "docker_image": self.docker_image,
            "config": self.config,
            "environment_vars": self.environment_vars,
            "max_execution_time": self.max_execution_time,
            "max_memory_mb": self.max_memory_mb,
            "max_cpu_percent": self.max_cpu_percent,
            "total_executions": self.total_executions,
            "successful_executions": self.successful_executions,
            "failed_executions": self.failed_executions,
            "success_rate": self.success_rate,
            "average_execution_time": self.average_execution_time,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_executed_at": self.last_executed_at.isoformat() if self.last_executed_at else None,
            "owner_id": self.owner_id,
        }
