"""
Notification tasks
"""

from app.core.celery import celery_app
from app.core.database import AsyncSessionLocal
from app.core.socketio_manager import send_to_user, broadcast
from loguru import logger
import asyncio
from datetime import datetime


@celery_app.task
def send_user_notification(user_id: int, notification_type: str, title: str, message: str, data: dict = None):
    """Send notification to a specific user"""
    
    async def _send():
        try:
            notification_data = {
                "type": notification_type,
                "title": title,
                "message": message,
                "data": data or {},
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Send via WebSocket
            await send_to_user(user_id, "notification", notification_data)
            
            logger.info(f"Sent notification to user {user_id}: {title}")
            
            return {"success": True, "user_id": user_id}
            
        except Exception as e:
            logger.error(f"Failed to send notification to user {user_id}: {e}")
            raise
    
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(_send())
    finally:
        loop.close()


@celery_app.task
def send_broadcast_notification(notification_type: str, title: str, message: str, data: dict = None):
    """Send notification to all connected users"""
    
    async def _broadcast():
        try:
            notification_data = {
                "type": notification_type,
                "title": title,
                "message": message,
                "data": data or {},
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Broadcast via WebSocket
            await broadcast("notification", notification_data)
            
            logger.info(f"Broadcasted notification: {title}")
            
            return {"success": True, "type": "broadcast"}
            
        except Exception as e:
            logger.error(f"Failed to broadcast notification: {e}")
            raise
    
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(_broadcast())
    finally:
        loop.close()


@celery_app.task
def send_task_failure_notification(task_id: str, error_message: str):
    """Send notification about task failure"""
    
    async def _notify():
        try:
            # In a real implementation, you would:
            # 1. Get task details from database
            # 2. Find the user who initiated the task
            # 3. Send appropriate notification
            
            logger.warning(f"Task {task_id} failed: {error_message}")
            
            # For now, just log the failure
            # Could also send to monitoring systems
            
            return {"success": True, "task_id": task_id}
            
        except Exception as e:
            logger.error(f"Failed to send task failure notification: {e}")
            raise
    
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(_notify())
    finally:
        loop.close()


@celery_app.task
def send_agent_status_notification(agent_id: int, user_id: int, status: str, message: str = None):
    """Send agent status change notification"""
    
    async def _notify():
        try:
            notification_data = {
                "agent_id": agent_id,
                "status": status,
                "message": message or f"Agent status changed to {status}",
                "timestamp": datetime.utcnow().isoformat()
            }
            
            await send_to_user(user_id, "agent_status_change", notification_data)
            
            logger.info(f"Sent agent status notification to user {user_id}: Agent {agent_id} -> {status}")
            
            return {"success": True, "agent_id": agent_id, "user_id": user_id}
            
        except Exception as e:
            logger.error(f"Failed to send agent status notification: {e}")
            raise
    
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(_notify())
    finally:
        loop.close()


@celery_app.task
def send_system_maintenance_notification(maintenance_type: str, start_time: str, duration: str):
    """Send system maintenance notification"""
    
    async def _notify():
        try:
            notification_data = {
                "type": "system_maintenance",
                "maintenance_type": maintenance_type,
                "start_time": start_time,
                "duration": duration,
                "message": f"System maintenance scheduled: {maintenance_type}",
                "timestamp": datetime.utcnow().isoformat()
            }
            
            await broadcast("system_notification", notification_data)
            
            logger.info(f"Sent system maintenance notification: {maintenance_type}")
            
            return {"success": True, "type": maintenance_type}
            
        except Exception as e:
            logger.error(f"Failed to send maintenance notification: {e}")
            raise
    
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(_notify())
    finally:
        loop.close()


@celery_app.task
def process_notification_queue():
    """Process queued notifications"""
    
    async def _process():
        try:
            # In a real implementation, you would:
            # 1. Get pending notifications from database
            # 2. Process each notification based on type
            # 3. Send via appropriate channels (WebSocket, email, SMS, etc.)
            # 4. Mark notifications as sent
            
            logger.info("Processing notification queue")
            
            return {"success": True, "processed": 0}
            
        except Exception as e:
            logger.error(f"Failed to process notification queue: {e}")
            raise
    
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(_process())
    finally:
        loop.close()
