import asyncio
from app.core.database import get_async_db
from app.services.user_service import UserService

async def check_users():
    async for db in get_async_db():
        user_service = UserService(db)
        users = await user_service.get_all()
        print(f'Total users: {len(users)}')
        for u in users[:10]:
            print(f'Email: {u.email}, Active: {u.is_active}, Username: {u.username}')
        break

if __name__ == '__main__':
    asyncio.run(check_users())