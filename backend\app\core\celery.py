"""
Celery configuration for background tasks
"""

from celery import Celery
from kombu import Queue
from app.core.config import settings

# Create Celery app
celery_app = Celery(
    "agentico",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=[
        "app.tasks.agent_tasks",
        "app.tasks.file_tasks",
        "app.tasks.notification_tasks",
        "app.tasks.cleanup_tasks"
    ]
)

# Celery configuration
celery_app.conf.update(
    # Task routing
    task_routes={
        "app.tasks.agent_tasks.*": {"queue": "agent_queue"},
        "app.tasks.file_tasks.*": {"queue": "file_queue"},
        "app.tasks.notification_tasks.*": {"queue": "notification_queue"},
        "app.tasks.cleanup_tasks.*": {"queue": "cleanup_queue"},
    },
    
    # Queue configuration
    task_queues=(
        Queue("agent_queue", routing_key="agent"),
        Queue("file_queue", routing_key="file"),
        Queue("notification_queue", routing_key="notification"),
        Queue("cleanup_queue", routing_key="cleanup"),
    ),
    
    # Task settings
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    
    # Result settings
    result_expires=3600,  # 1 hour
    result_persistent=True,
    
    # Worker settings
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    worker_disable_rate_limits=False,
    
    # Task execution settings
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    task_time_limit=settings.AGENT_TIMEOUT_SECONDS,
    task_soft_time_limit=settings.AGENT_TIMEOUT_SECONDS - 30,
    
    # Monitoring
    worker_send_task_events=True,
    task_send_sent_event=True,
    
    # Beat schedule for periodic tasks
    beat_schedule={
        "cleanup-expired-tasks": {
            "task": "app.tasks.cleanup_tasks.cleanup_expired_tasks",
            "schedule": 3600.0,  # Every hour
        },
        "cleanup-old-files": {
            "task": "app.tasks.cleanup_tasks.cleanup_old_files",
            "schedule": 86400.0,  # Every day
        },
        "health-check": {
            "task": "app.tasks.cleanup_tasks.health_check",
            "schedule": 300.0,  # Every 5 minutes
        },
    },
)


@celery_app.task(bind=True)
def debug_task(self):
    """Debug task for testing Celery"""
    print(f"Request: {self.request!r}")
    return "Debug task completed"


# Task result states
class TaskState:
    PENDING = "PENDING"
    STARTED = "STARTED"
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"
    RETRY = "RETRY"
    REVOKED = "REVOKED"


# Custom task base class
class AgenticoTask(celery_app.Task):
    """Base task class with custom error handling"""
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Handle task failure"""
        from loguru import logger
        logger.error(f"Task {task_id} failed: {exc}")
        
        # Send notification about task failure
        from app.tasks.notification_tasks import send_task_failure_notification
        send_task_failure_notification.delay(task_id, str(exc))
    
    def on_success(self, retval, task_id, args, kwargs):
        """Handle task success"""
        from loguru import logger
        logger.info(f"Task {task_id} completed successfully")
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """Handle task retry"""
        from loguru import logger
        logger.warning(f"Task {task_id} retrying: {exc}")


# Set default task base
celery_app.Task = AgenticoTask
