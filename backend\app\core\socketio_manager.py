"""
Socket.IO manager for real-time communication
"""

import socketio
from typing import Dict, List, Optional
from loguru import logger
import json
from datetime import datetime

from app.core.config import settings
from app.core.security import verify_token
from app.core.exceptions import AuthenticationError

# Create Socket.IO server
sio = socketio.AsyncServer(
    cors_allowed_origins="*" if settings.ENVIRONMENT == "development" else settings.ALLOWED_HOSTS,
    ping_timeout=settings.WS_HEARTBEAT_TIMEOUT,
    ping_interval=settings.WS_HEARTBEAT_INTERVAL,
    logger=settings.DEBUG,
    engineio_logger=settings.DEBUG
)

# Store active connections
active_connections: Dict[str, Dict] = {}
user_sessions: Dict[str, List[str]] = {}


@sio.event
async def connect(sid: str, environ: dict, auth: Optional[dict] = None):
    """Handle client connection"""
    try:
        logger.info(f"Client {sid} attempting to connect")
        
        # Authenticate user if auth token provided
        user_id = None
        if auth and "token" in auth:
            try:
                payload = verify_token(auth["token"])
                user_id = payload.get("sub")
                logger.info(f"Authenticated user {user_id} for session {sid}")
            except AuthenticationError:
                logger.warning(f"Authentication failed for session {sid}")
                await sio.disconnect(sid)
                return False
        
        # Store connection info
        active_connections[sid] = {
            "user_id": user_id,
            "connected_at": datetime.utcnow().isoformat(),
            "last_activity": datetime.utcnow().isoformat()
        }
        
        # Track user sessions
        if user_id:
            if user_id not in user_sessions:
                user_sessions[user_id] = []
            user_sessions[user_id].append(sid)
        
        # Join user-specific room if authenticated
        if user_id:
            await sio.enter_room(sid, f"user_{user_id}")
        
        # Send welcome message
        await sio.emit("connected", {
            "message": "Connected to Agentico",
            "session_id": sid,
            "authenticated": user_id is not None
        }, room=sid)
        
        logger.info(f"Client {sid} connected successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error during connection for {sid}: {e}")
        await sio.disconnect(sid)
        return False


@sio.event
async def disconnect(sid: str):
    """Handle client disconnection"""
    try:
        logger.info(f"Client {sid} disconnecting")
        
        # Get connection info
        connection_info = active_connections.get(sid)
        if connection_info:
            user_id = connection_info.get("user_id")
            
            # Remove from user sessions
            if user_id and user_id in user_sessions:
                if sid in user_sessions[user_id]:
                    user_sessions[user_id].remove(sid)
                if not user_sessions[user_id]:
                    del user_sessions[user_id]
        
        # Remove connection
        if sid in active_connections:
            del active_connections[sid]
        
        logger.info(f"Client {sid} disconnected")
        
    except Exception as e:
        logger.error(f"Error during disconnection for {sid}: {e}")


@sio.event
async def join_room(sid: str, data: dict):
    """Handle room join requests"""
    try:
        room_name = data.get("room")
        if not room_name:
            await sio.emit("error", {"message": "Room name required"}, room=sid)
            return
        
        # Update last activity
        if sid in active_connections:
            active_connections[sid]["last_activity"] = datetime.utcnow().isoformat()
        
        await sio.enter_room(sid, room_name)
        await sio.emit("room_joined", {"room": room_name}, room=sid)
        
        logger.info(f"Client {sid} joined room {room_name}")
        
    except Exception as e:
        logger.error(f"Error joining room for {sid}: {e}")
        await sio.emit("error", {"message": "Failed to join room"}, room=sid)


@sio.event
async def leave_room(sid: str, data: dict):
    """Handle room leave requests"""
    try:
        room_name = data.get("room")
        if not room_name:
            await sio.emit("error", {"message": "Room name required"}, room=sid)
            return
        
        # Update last activity
        if sid in active_connections:
            active_connections[sid]["last_activity"] = datetime.utcnow().isoformat()
        
        await sio.leave_room(sid, room_name)
        await sio.emit("room_left", {"room": room_name}, room=sid)
        
        logger.info(f"Client {sid} left room {room_name}")
        
    except Exception as e:
        logger.error(f"Error leaving room for {sid}: {e}")
        await sio.emit("error", {"message": "Failed to leave room"}, room=sid)


@sio.event
async def ping(sid: str, data: dict):
    """Handle ping requests"""
    try:
        # Update last activity
        if sid in active_connections:
            active_connections[sid]["last_activity"] = datetime.utcnow().isoformat()
        
        await sio.emit("pong", {"timestamp": datetime.utcnow().isoformat()}, room=sid)
        
    except Exception as e:
        logger.error(f"Error handling ping for {sid}: {e}")


# Utility functions for sending messages
async def send_to_user(user_id: str, event: str, data: dict):
    """Send message to all sessions of a specific user"""
    try:
        room = f"user_{user_id}"
        await sio.emit(event, data, room=room)
        logger.debug(f"Sent {event} to user {user_id}")
    except Exception as e:
        logger.error(f"Error sending message to user {user_id}: {e}")


async def send_to_room(room: str, event: str, data: dict):
    """Send message to a specific room"""
    try:
        await sio.emit(event, data, room=room)
        logger.debug(f"Sent {event} to room {room}")
    except Exception as e:
        logger.error(f"Error sending message to room {room}: {e}")


async def broadcast(event: str, data: dict):
    """Broadcast message to all connected clients"""
    try:
        await sio.emit(event, data)
        logger.debug(f"Broadcasted {event}")
    except Exception as e:
        logger.error(f"Error broadcasting {event}: {e}")


def get_active_connections() -> Dict[str, Dict]:
    """Get all active connections"""
    return active_connections.copy()


def get_user_sessions(user_id: str) -> List[str]:
    """Get all sessions for a specific user"""
    return user_sessions.get(user_id, []).copy()


def is_user_online(user_id: str) -> bool:
    """Check if user is online"""
    return user_id in user_sessions and len(user_sessions[user_id]) > 0
