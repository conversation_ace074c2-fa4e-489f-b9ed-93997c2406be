'use client';

import { useState, useRef, useEffect } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { Send, Bot, User, Zap, Code, Search, BarChart3, FileText, Image, Settings, Workflow } from 'lucide-react';

interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  metadata?: any;
}

interface SuperAgentInfo {
  id: number;
  name: string;
  description: string;
  status: string;
  capabilities: string[];
  model: string;
  total_executions: number;
  success_rate: number;
  average_execution_time?: string;
}

interface SuperAgentCapability {
  name: string;
  description: string;
  icon: string;
}

// Helper function to get auth token
const getAuthToken = () => {
  const authStorage = localStorage.getItem('auth-storage');
  if (authStorage) {
    try {
      const { state } = JSON.parse(authStorage);
      return state?.token;
    } catch (error) {
      console.error('Error parsing auth storage:', error);
    }
  }
  return null;
};

// API service for super agent
const superAgentService = {
  async getInfo(): Promise<SuperAgentInfo> {
    const token = getAuthToken();
    const response = await fetch('/api/v1/super-agent/info', {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      }
    });
    if (!response.ok) throw new Error('Failed to get super agent info');
    return response.json();
  },

  async getCapabilities(): Promise<{ capabilities: SuperAgentCapability[] }> {
    const token = getAuthToken();
    const response = await fetch('/api/v1/super-agent/capabilities', {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      }
    });
    if (!response.ok) throw new Error('Failed to get capabilities');
    return response.json();
  },

  async chat(messages: ChatMessage[], stream: boolean = true) {
    const token = getAuthToken();
    const response = await fetch('/api/v1/super-agent/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      },
      body: JSON.stringify({
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content,
          timestamp: msg.timestamp.toISOString(),
          metadata: msg.metadata || {}
        })),
        stream,
        context: {}
      })
    });
    
    if (!response.ok) throw new Error('Failed to chat with super agent');
    return response;
  }
};

const getCapabilityIcon = (iconText: string) => {
  const iconMap: { [key: string]: any } = {
    '💬': User,
    '🔧': Code,
    '🔍': Search,
    '📊': BarChart3,
    '📁': FileText,
    '🎨': Image,
    '⚙️': Settings,
    '🔄': Workflow
  };
  return iconMap[iconText] || Zap;
};

export default function SuperAgentChatPage() {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      role: 'assistant',
      content: 'Hello! I\'m your Universal AI Assistant. I can help you with coding, research, data analysis, creative tasks, automation, and much more. What would you like to work on today?',
      timestamp: new Date(),
      metadata: {}
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Fetch super agent info
  const { data: agentInfo } = useQuery({
    queryKey: ['super-agent-info'],
    queryFn: () => superAgentService.getInfo(),
  });

  // Fetch capabilities
  const { data: capabilitiesData } = useQuery({
    queryKey: ['super-agent-capabilities'],
    queryFn: () => superAgentService.getCapabilities(),
  });

  // Chat mutation
  const chatMutation = useMutation({
    mutationFn: (messages: ChatMessage[]) => superAgentService.chat(messages),
    onSuccess: async (response) => {
      if (response.body) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let assistantMessage = '';
        
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            
            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');
            
            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const data = JSON.parse(line.slice(6));
                  if (data.content) {
                    assistantMessage += data.content;
                    // Update the last message in real-time
                    setMessages(prev => {
                      const newMessages = [...prev];
                      if (newMessages[newMessages.length - 1]?.role === 'assistant') {
                        newMessages[newMessages.length - 1].content = assistantMessage;
                      } else {
                        newMessages.push({
                          role: 'assistant',
                          content: assistantMessage,
                          timestamp: new Date(),
                          metadata: data.metadata
                        });
                      }
                      return newMessages;
                    });
                  }
                  
                  if (data.error) {
                    toast.error(data.error);
                    break;
                  }
                } catch (e) {
                  // Ignore parsing errors for incomplete chunks
                }
              }
            }
          }
        } catch (error) {
          console.error('Streaming error:', error);
          toast.error('Connection error occurred');
        }
      }
      setIsLoading(false);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to send message');
      setIsLoading(false);
    },
  });

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      role: 'user',
      content: inputMessage.trim(),
      timestamp: new Date(),
      metadata: {}
    };

    const newMessages = [...messages, userMessage];
    setMessages(newMessages);
    setInputMessage('');
    setIsLoading(true);

    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
    }

    chatMutation.mutate(newMessages);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputMessage(e.target.value);
    
    // Auto-resize textarea
    const textarea = e.target;
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar with agent info and capabilities */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        {/* Agent Info Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
              <Bot className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">
                {agentInfo?.name || 'Universal AI Assistant'}
              </h1>
              <p className="text-sm text-gray-500">
                Status: <span className="text-green-600 font-medium">{agentInfo?.status || 'Active'}</span>
              </p>
            </div>
          </div>
          
          {agentInfo && (
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Model:</span>
                <span className="font-medium">{agentInfo.model}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Executions:</span>
                <span className="font-medium">{agentInfo.total_executions}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Success Rate:</span>
                <span className="font-medium text-green-600">{agentInfo.success_rate}%</span>
              </div>
            </div>
          )}
        </div>

        {/* Capabilities */}
        <div className="flex-1 p-6 overflow-y-auto">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Capabilities</h2>
          <div className="space-y-3">
            {capabilitiesData?.capabilities.map((capability, index) => {
              const IconComponent = getCapabilityIcon(capability.icon);
              return (
                <div key={index} className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <IconComponent className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <div>
                      <h3 className="font-medium text-gray-900 text-sm">{capability.name}</h3>
                      <p className="text-xs text-gray-600 mt-1">{capability.description}</p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Main chat area */}
      <div className="flex-1 flex flex-col">
        {/* Chat header */}
        <div className="bg-white border-b border-gray-200 p-4">
          <h2 className="text-lg font-semibold text-gray-900">Chat with Universal AI Assistant</h2>
          <p className="text-sm text-gray-600">Ask me anything - I'll adapt to help you with any task!</p>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message, index) => (
            <div key={index} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div className={`flex space-x-3 max-w-3xl ${
                message.role === 'user' ? 'flex-row-reverse space-x-reverse' : ''
              }`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                  message.role === 'user' 
                    ? 'bg-blue-600' 
                    : 'bg-gradient-to-br from-blue-500 to-purple-600'
                }`}>
                  {message.role === 'user' ? (
                    <User className="w-4 h-4 text-white" />
                  ) : (
                    <Bot className="w-4 h-4 text-white" />
                  )}
                </div>
                <div className={`rounded-lg p-4 ${
                  message.role === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white border border-gray-200 text-gray-900'
                }`}>
                  <div className="whitespace-pre-wrap">{message.content}</div>
                  <div className={`text-xs mt-2 ${
                    message.role === 'user' ? 'text-blue-100' : 'text-gray-500'
                  }`}>
                    {message.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              </div>
            </div>
          ))}
          
          {isLoading && (
            <div className="flex justify-start">
              <div className="flex space-x-3 max-w-3xl">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center flex-shrink-0">
                  <Bot className="w-4 h-4 text-white" />
                </div>
                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* Input area */}
        <div className="bg-white border-t border-gray-200 p-4">
          <div className="flex space-x-4">
            <div className="flex-1">
              <textarea
                ref={textareaRef}
                value={inputMessage}
                onChange={handleTextareaChange}
                onKeyPress={handleKeyPress}
                placeholder="Type your message here... (Press Enter to send, Shift+Enter for new line)"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                rows={1}
                style={{ minHeight: '48px', maxHeight: '120px' }}
                disabled={isLoading}
              />
            </div>
            <button
              onClick={handleSendMessage}
              disabled={!inputMessage.trim() || isLoading}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              <Send className="w-4 h-4" />
              <span>Send</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}