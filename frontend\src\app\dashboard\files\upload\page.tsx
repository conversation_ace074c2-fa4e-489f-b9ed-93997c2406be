'use client';

import React, { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useDropzone } from 'react-dropzone';
import { toast } from 'sonner';
import {
  ArrowLeftIcon,
  CloudArrowUpIcon,
  DocumentIcon,
  XMarkIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
} from '@heroicons/react/24/outline';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { fileService } from '@/services/file-service';
import type { FileUploadProgress } from '@/types/file';

export default function FileUploadPage() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [uploadProgress, setUploadProgress] = useState<FileUploadProgress[]>([]);
  const [isDragActive, setIsDragActive] = useState(false);

  // Upload mutation
  const uploadMutation = useMutation({
    mutationFn: async (file: File) => {
      const uploadId = Date.now() + Math.random();
      const progressItem: FileUploadProgress = {
        file,
        progress: 0,
        status: 'uploading',
      };

      setUploadProgress(prev => [...prev, progressItem]);

      try {
        const result = await fileService.uploadFile(file, (progress) => {
          setUploadProgress(prev =>
            prev.map(item =>
              item.file === file
                ? { ...item, progress }
                : item
            )
          );
        });

        setUploadProgress(prev =>
          prev.map(item =>
            item.file === file
              ? { ...item, status: 'completed', result }
              : item
          )
        );

        return result;
      } catch (error) {
        setUploadProgress(prev =>
          prev.map(item =>
            item.file === file
              ? { ...item, status: 'error', error: 'Upload failed' }
              : item
          )
        );
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['files'] });
      queryClient.invalidateQueries({ queryKey: ['file-stats'] });
      toast.success('File uploaded successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Upload failed');
    },
  });

  const onDrop = useCallback((acceptedFiles: File[]) => {
    acceptedFiles.forEach(file => {
      uploadMutation.mutate(file);
    });
  }, [uploadMutation]);

  const { getRootProps, getInputProps, isDragActive: dropzoneActive } = useDropzone({
    onDrop,
    multiple: true,
    maxSize: 100 * 1024 * 1024, // 100MB
    accept: {
      'text/*': ['.txt', '.md', '.csv', '.json'],
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.svg'],
    },
    onDragEnter: () => setIsDragActive(true),
    onDragLeave: () => setIsDragActive(false),
    onDropAccepted: () => setIsDragActive(false),
    onDropRejected: () => setIsDragActive(false),
  });

  const removeUploadItem = (file: File) => {
    setUploadProgress(prev => prev.filter(item => item.file !== file));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) {
      return '🖼️';
    } else if (file.type === 'application/pdf') {
      return '📄';
    } else if (file.type.includes('word')) {
      return '📝';
    } else if (file.type.includes('excel') || file.type.includes('spreadsheet')) {
      return '📊';
    } else {
      return '📄';
    }
  };

  const completedUploads = uploadProgress.filter(item => item.status === 'completed').length;
  const totalUploads = uploadProgress.length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeftIcon className="h-4 w-4" />
          Back
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Upload Files</h1>
          <p className="mt-1 text-sm text-gray-500">
            Upload files for your AI agents to process and analyze
          </p>
        </div>
      </div>

      {/* Upload Area */}
      <Card className="p-6">
        <div
          {...getRootProps()}
          className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer ${
            dropzoneActive || isDragActive
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-300 hover:border-gray-400'
          }`}
        >
          <input {...getInputProps()} />
          <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-4 text-lg font-medium text-gray-900">
            {dropzoneActive ? 'Drop files here' : 'Upload files'}
          </h3>
          <p className="mt-2 text-sm text-gray-500">
            Drag and drop files here, or click to select files
          </p>
          <p className="mt-1 text-xs text-gray-400">
            Supports: PDF, DOC, DOCX, XLS, XLSX, TXT, MD, CSV, JSON, Images (max 100MB each)
          </p>
        </div>
      </Card>

      {/* Upload Progress */}
      {uploadProgress.length > 0 && (
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Upload Progress</h3>
            <span className="text-sm text-gray-500">
              {completedUploads} of {totalUploads} completed
            </span>
          </div>
          
          <div className="space-y-3">
            {uploadProgress.map((item, index) => (
              <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                <div className="text-2xl">{getFileIcon(item.file)}</div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {item.file.name}
                    </p>
                    <span className="text-xs text-gray-500">
                      {formatFileSize(item.file.size)}
                    </span>
                  </div>
                  
                  {item.status === 'uploading' && (
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${item.progress}%` }}
                      />
                    </div>
                  )}
                  
                  {item.status === 'completed' && (
                    <div className="flex items-center gap-1 text-green-600">
                      <CheckCircleIcon className="h-4 w-4" />
                      <span className="text-xs">Upload completed</span>
                    </div>
                  )}
                  
                  {item.status === 'error' && (
                    <div className="flex items-center gap-1 text-red-600">
                      <ExclamationCircleIcon className="h-4 w-4" />
                      <span className="text-xs">{item.error || 'Upload failed'}</span>
                    </div>
                  )}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => removeUploadItem(item.file)}
                  className="h-8 w-8 p-0"
                >
                  <XMarkIcon className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
          
          {completedUploads === totalUploads && totalUploads > 0 && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 text-green-600">
                  <CheckCircleIcon className="h-5 w-5" />
                  <span className="text-sm font-medium">All files uploaded successfully!</span>
                </div>
                <Button
                  onClick={() => router.push('/dashboard/files')}
                  className="flex items-center gap-2"
                >
                  <DocumentIcon className="h-4 w-4" />
                  View Files
                </Button>
              </div>
            </div>
          )}
        </Card>
      )}

      {/* Upload Tips */}
      <Card className="p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Upload Tips</h3>
        <ul className="space-y-2 text-sm text-gray-600">
          <li className="flex items-start gap-2">
            <span className="text-blue-500 mt-0.5">•</span>
            <span>Maximum file size is 100MB per file</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-blue-500 mt-0.5">•</span>
            <span>Supported formats: PDF, Word documents, Excel files, text files, images</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-blue-500 mt-0.5">•</span>
            <span>Files will be processed and made available to your AI agents</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="text-blue-500 mt-0.5">•</span>
            <span>You can upload multiple files at once by selecting them or dragging them here</span>
          </li>
        </ul>
      </Card>
    </div>
  );
}