@echo off
REM Agentico Deployment Script for Windows
REM This script sets up and deploys the Agentico AI Agent Platform

echo 🚀 Starting Agentico Deployment...
echo.

REM Check if Docker is installed
set "DOCKER_PATH=C:\Program Files\Docker\Docker\resources\bin\docker.exe"
if exist "%DOCKER_PATH%" (
    "%DOCKER_PATH%" --version >nul 2>&1
) else (
    docker --version >nul 2>&1
)
if %errorlevel% neq 0 (
    echo [ERROR] Docker is not installed. Please install Docker Desktop first.
    echo Download from: https://www.docker.com/products/docker-desktop/
    pause
    exit /b 1
)

REM Check Docker Compose
if exist "%DOCKER_PATH%" (
    "%DOCKER_PATH%" compose version >nul 2>&1
) else (
    docker-compose --version >nul 2>&1
)
if %errorlevel% neq 0 (
    echo [ERROR] Docker Compose is not installed. Please install Docker Compose first.
    pause
    exit /b 1
)

echo [SUCCESS] Docker and Docker Compose are installed

REM Check if Docker Desktop is running
if exist "%DOCKER_PATH%" (
    "%DOCKER_PATH%" info >nul 2>&1
) else (
    docker info >nul 2>&1
)
if %errorlevel% neq 0 (
    echo [ERROR] Docker Desktop is not running!
    echo.
    echo Please start Docker Desktop and wait for it to be ready, then run this script again.
    echo.
    echo Steps:
    echo 1. Open Docker Desktop from Start Menu
    echo 2. Wait for the Docker icon in system tray to show "Docker Desktop is running"
    echo 3. Run this script again
    echo.
    pause
    exit /b 1
)

echo [SUCCESS] Docker Desktop is running
echo.

REM Check if .env file exists
if not exist .env (
    echo [WARNING] .env file not found. Creating from template...
    copy .env.example .env
    echo [WARNING] Please edit .env file with your configuration before continuing.
    echo [WARNING] Especially set your API keys and change the default secrets!
    echo.
    echo Press any key to continue after editing .env file...
    pause >nul
) else (
    echo [SUCCESS] .env file found
)

echo.

REM Create necessary directories
echo [INFO] Creating necessary directories...
if not exist backend\logs mkdir backend\logs
if not exist backend\uploads mkdir backend\uploads
if not exist backend\temp mkdir backend\temp
if not exist frontend\.next mkdir frontend\.next
echo [SUCCESS] Directories created
echo.

REM Stop any existing containers
echo [INFO] Stopping existing containers...
if exist "%DOCKER_PATH%" (
    "%DOCKER_PATH%" compose down
) else (
    docker-compose down
)
echo.

REM Build images
echo [INFO] Building Docker images...
if exist "%DOCKER_PATH%" (
    "%DOCKER_PATH%" compose build
) else (
    docker-compose build
)
if %errorlevel% neq 0 (
    echo [ERROR] Failed to build Docker images
    pause
    exit /b 1
)
echo.

REM Start services
echo [INFO] Starting services...
if exist "%DOCKER_PATH%" (
    "%DOCKER_PATH%" compose up -d
) else (
    docker-compose up -d
)
if %errorlevel% neq 0 (
    echo [ERROR] Failed to start services
    pause
    exit /b 1
)
echo.

REM Wait for services to be ready
echo [INFO] Waiting for services to be ready...
echo This may take a few minutes...
echo.

REM Wait for backend to be ready
:wait_backend
ping 127.0.0.1 -n 6 >nul
curl -f http://localhost:8000/health >nul 2>&1
if %errorlevel% neq 0 (
    echo Waiting for backend API...
    goto wait_backend
)

echo [SUCCESS] All services are ready!
echo.

REM Show deployment status
echo 🎉 Agentico Deployment Complete!
echo.
echo 📊 Service Status:
if exist "%DOCKER_PATH%" (
    "%DOCKER_PATH%" compose ps
) else (
    docker-compose ps
)
echo.
echo 🌐 Access URLs:
echo   Frontend:      http://localhost:3001
echo   Backend API:   http://localhost:8000
echo   API Docs:      http://localhost:8000/docs
echo   File Storage:  Local file system (no web interface)
echo.
echo 📝 Next Steps:
echo   1. Visit http://localhost:3001 to access the platform
echo   2. Create your first user account
echo   3. Set up your AI API keys in the settings
echo   4. Create your first AI agent
echo.
echo 🔧 Management Commands:
echo   View logs:    docker compose logs -f [service]
echo   Stop:         docker compose down
echo   Restart:      docker compose restart [service]
echo   Update:       git pull ^&^& docker compose build ^&^& docker compose up -d
echo.

REM Open browser
echo Opening Agentico in your default browser...
start http://localhost:3001

echo.
echo Press any key to exit...
pause >nul
