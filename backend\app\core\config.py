"""
Application configuration settings
"""

from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings"""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=True
    )
    
    # Application
    APP_NAME: str = "Agentico"
    ENVIRONMENT: str = Field(default="development", description="Environment: development, staging, production")
    DEBUG: bool = Field(default=True, description="Debug mode")
    
    # API
    API_V1_STR: str = "/api/v1"
    ALLOWED_HOSTS: List[str] = Field(default=["*"], description="Allowed hosts for CORS")
    
    # Database
    DATABASE_URL: str = Field(
        default="postgresql://postgres:postgres@localhost:5432/agentico",
        description="Database connection URL"
    )
    
    # Redis
    REDIS_URL: str = Field(
        default="redis://localhost:6379",
        description="Redis connection URL"
    )
    
    # Security
    JWT_SECRET_KEY: str = Field(
        default="your-super-secret-jwt-key-change-in-production",
        description="JWT secret key"
    )
    JWT_ALGORITHM: str = Field(default="HS256", description="JWT algorithm")
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, description="JWT access token expiration in minutes")
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = Field(default=7, description="JWT refresh token expiration in days")
    
    # Password hashing
    PWD_CONTEXT_SCHEMES: List[str] = Field(default=["bcrypt"], description="Password hashing schemes")
    PWD_CONTEXT_DEPRECATED: str = Field(default="auto", description="Deprecated password schemes")
    
    # File Storage (Local)
    FILE_STORAGE_TYPE: str = Field(default="local", description="Storage type: local, s3, or minio")
    FILE_STORAGE_PATH: str = Field(default="/app/storage", description="Local file storage path")
    FILE_UPLOAD_PATH: str = Field(default="/app/uploads", description="File upload temporary path")
    MAX_FILE_SIZE_MB: int = Field(default=100, description="Maximum file size in MB")
    ALLOWED_FILE_TYPES: List[str] = Field(
        default=["txt", "pdf", "doc", "docx", "jpg", "jpeg", "png", "gif", "csv", "json", "xml"],
        description="Allowed file extensions"
    )
    
    # Celery
    CELERY_BROKER_URL: str = Field(default="redis://localhost:6379/0", description="Celery broker URL")
    CELERY_RESULT_BACKEND: str = Field(default="redis://localhost:6379/0", description="Celery result backend")
    
    # AI Models
    OPENAI_API_KEY: Optional[str] = Field(default=None, description="OpenAI API key")
    ANTHROPIC_API_KEY: Optional[str] = Field(default=None, description="Anthropic API key")
    OPENROUTER_API_KEY: Optional[str] = Field(default=None, description="OpenRouter API key")
    DEFAULT_LLM_MODEL: str = Field(default="gpt-3.5-turbo", description="Default LLM model")
    
    # Agent Configuration
    MAX_CONCURRENT_AGENTS: int = Field(default=10, description="Maximum concurrent agents")
    AGENT_TIMEOUT_SECONDS: int = Field(default=300, description="Agent execution timeout in seconds")
    DOCKER_ENABLED: bool = Field(default=True, description="Enable Docker for agent execution")
    
    # Logging
    LOG_LEVEL: str = Field(default="INFO", description="Logging level")
    LOG_FORMAT: str = Field(
        default="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        description="Log format"
    )
    
    # Monitoring
    ENABLE_METRICS: bool = Field(default=True, description="Enable Prometheus metrics")
    METRICS_PORT: int = Field(default=8001, description="Metrics server port")
    
    # Rate Limiting
    RATE_LIMIT_ENABLED: bool = Field(default=True, description="Enable rate limiting")
    RATE_LIMIT_REQUESTS: int = Field(default=100, description="Rate limit requests per minute")
    
    # WebSocket
    WS_HEARTBEAT_INTERVAL: int = Field(default=25, description="WebSocket heartbeat interval in seconds")
    WS_HEARTBEAT_TIMEOUT: int = Field(default=60, description="WebSocket heartbeat timeout in seconds")
    
    @property
    def database_url_sync(self) -> str:
        """Get synchronous database URL"""
        return self.DATABASE_URL.replace("postgresql://", "postgresql+psycopg2://")
    
    @property
    def database_url_async(self) -> str:
        """Get asynchronous database URL"""
        return self.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")


# Create settings instance
settings = Settings()
