import { apiClient } from '@/lib/api-client';
import type {
  User,
  LoginCredentials,
  RegisterData,
  AuthResponse,
  TokenRefreshResponse,
} from '@/types/auth';

class AuthService {
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await apiClient.post('/auth/login', credentials);
    return response.data;
  }

  async register(data: RegisterData): Promise<{ message: string; user: User }> {
    const response = await apiClient.post('/auth/register', data);
    return response.data;
  }

  async logout(): Promise<void> {
    await apiClient.post('/auth/logout');
  }

  async getCurrentUser(): Promise<User> {
    const response = await apiClient.get('/auth/me');
    return response.data;
  }

  async refreshToken(refreshToken: string): Promise<TokenRefreshResponse> {
    const response = await apiClient.post('/auth/refresh', {
      refresh_token: refreshToken,
    });
    return response.data;
  }

  async requestPasswordReset(email: string): Promise<{ message: string }> {
    const response = await apiClient.post('/auth/password-reset', { email });
    return response.data;
  }

  async confirmPasswordReset(
    token: string,
    newPassword: string
  ): Promise<{ message: string }> {
    const response = await apiClient.post('/auth/password-reset/confirm', {
      token,
      new_password: newPassword,
    });
    return response.data;
  }
}

export const authService = new AuthService();
