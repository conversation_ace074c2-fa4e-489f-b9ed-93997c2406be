#!/usr/bin/env python3
import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, '/app')

from app.core.database import get_async_db
from app.services.user_service import UserService
from app.core.security import verify_token
from app.core.exceptions import AuthenticationError

async def test_auth():
    token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************.w1g2levhI75djjNH4zRXxuqy2IdDv60J_Ds53SxsmT4'
    
    print("=== Authentication Debug Test ===")
    
    try:
        # Step 1: Verify token
        print("Step 1: Verifying token...")
        payload = verify_token(token)
        print(f"✓ Token verified successfully")
        print(f"  Payload: {payload}")
        
        user_id = payload.get('sub')
        print(f"  User ID from token: {user_id}")
        
        # Step 2: Get database connection
        print("\nStep 2: Getting database connection...")
        db_gen = get_async_db()
        db = await anext(db_gen)
        print("✓ Database connection established")
        
        # Step 3: Look up user
        print("\nStep 3: Looking up user in database...")
        user_service = UserService(db)
        user = await user_service.get_by_id(user_id)
        
        if user is None:
            print("✗ User not found in database")
            return
        
        print(f"✓ User found: {user.email}")
        print(f"  User ID: {user.id}")
        print(f"  Is active: {user.is_active}")
        print(f"  Is verified: {user.is_verified}")
        
        if not user.is_active:
            print("✗ User account is disabled")
            return
            
        print("✓ Authentication should succeed")
        
        await db.close()
        
    except AuthenticationError as e:
        print(f"✗ Authentication error: {e}")
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_auth())