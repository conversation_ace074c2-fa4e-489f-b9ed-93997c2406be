#!/usr/bin/env python3
"""
Agentico Deployment Test Script
Tests all services and endpoints to verify deployment
"""

import requests
import time
import sys
import json
from typing import Dict, Any


class DeploymentTester:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:3000"
        self.results = {}
    
    def test_service(self, name: str, url: str, timeout: int = 10) -> bool:
        """Test if a service is responding"""
        try:
            response = requests.get(url, timeout=timeout)
            success = response.status_code == 200
            self.results[name] = {
                "status": "✅ PASS" if success else "❌ FAIL",
                "url": url,
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds()
            }
            return success
        except Exception as e:
            self.results[name] = {
                "status": "❌ FAIL",
                "url": url,
                "error": str(e)
            }
            return False
    
    def test_api_endpoint(self, name: str, endpoint: str) -> bool:
        """Test a specific API endpoint"""
        url = f"{self.base_url}{endpoint}"
        return self.test_service(name, url)
    
    def test_registration_flow(self) -> bool:
        """Test user registration"""
        try:
            # Test registration endpoint
            register_data = {
                "email": "<EMAIL>",
                "username": "testuser",
                "password": "testpassword123",
                "full_name": "Test User"
            }
            
            response = requests.post(
                f"{self.base_url}/api/v1/auth/register",
                json=register_data,
                timeout=10
            )
            
            success = response.status_code in [200, 201, 400]  # 400 if user exists
            self.results["User Registration"] = {
                "status": "✅ PASS" if success else "❌ FAIL",
                "status_code": response.status_code,
                "response": response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
            }
            return success
        except Exception as e:
            self.results["User Registration"] = {
                "status": "❌ FAIL",
                "error": str(e)
            }
            return False
    
    def test_agent_types(self) -> bool:
        """Test agent types endpoint"""
        try:
            response = requests.get(f"{self.base_url}/api/v1/agents/types", timeout=10)
            success = response.status_code == 200
            
            if success:
                data = response.json()
                agent_types = data.get("types", [])
                success = len(agent_types) > 0
            
            self.results["Agent Types"] = {
                "status": "✅ PASS" if success else "❌ FAIL",
                "status_code": response.status_code,
                "agent_types_count": len(agent_types) if success else 0
            }
            return success
        except Exception as e:
            self.results["Agent Types"] = {
                "status": "❌ FAIL",
                "error": str(e)
            }
            return False
    
    def run_all_tests(self):
        """Run all deployment tests"""
        print("🧪 Running Agentico Deployment Tests...")
        print("=" * 50)
        
        tests = [
            # Core services
            ("Backend Health", f"{self.base_url}/health"),
            ("Backend Root", f"{self.base_url}/"),
            ("API Documentation", f"{self.base_url}/docs"),
            ("Frontend", self.frontend_url),
            
            # API endpoints
            ("Auth Endpoints", f"{self.base_url}/api/v1/auth/register"),
            ("Agent Endpoints", f"{self.base_url}/api/v1/agents/types"),
            ("Health Check", f"{self.base_url}/api/v1/health"),
        ]
        
        # Test basic services
        for name, url in tests:
            print(f"Testing {name}...", end=" ")
            if self.test_service(name, url):
                print("✅")
            else:
                print("❌")
        
        # Test specific functionality
        print("\nTesting specific functionality...")
        
        print("Testing Agent Types...", end=" ")
        if self.test_agent_types():
            print("✅")
        else:
            print("❌")
        
        print("Testing User Registration...", end=" ")
        if self.test_registration_flow():
            print("✅")
        else:
            print("❌")
    
    def print_results(self):
        """Print detailed test results"""
        print("\n" + "=" * 50)
        print("📊 Detailed Test Results")
        print("=" * 50)
        
        passed = 0
        failed = 0
        
        for test_name, result in self.results.items():
            status = result["status"]
            print(f"\n{test_name}: {status}")
            
            if "✅" in status:
                passed += 1
            else:
                failed += 1
            
            # Print details
            if "url" in result:
                print(f"  URL: {result['url']}")
            if "status_code" in result:
                print(f"  Status Code: {result['status_code']}")
            if "response_time" in result:
                print(f"  Response Time: {result['response_time']:.3f}s")
            if "error" in result:
                print(f"  Error: {result['error']}")
            if "agent_types_count" in result:
                print(f"  Agent Types Found: {result['agent_types_count']}")
        
        print("\n" + "=" * 50)
        print(f"📈 Summary: {passed} passed, {failed} failed")
        
        if failed == 0:
            print("🎉 All tests passed! Deployment is successful!")
            print("\n🚀 Next Steps:")
            print("1. Visit http://localhost:3000 to access Agentico")
            print("2. Create your first user account")
            print("3. Set up your AI API keys")
            print("4. Create your first AI agent")
        else:
            print("⚠️  Some tests failed. Check the logs and try again.")
            print("\n🔧 Troubleshooting:")
            print("1. Ensure all Docker containers are running: docker-compose ps")
            print("2. Check logs: docker-compose logs")
            print("3. Wait a few more minutes for services to fully start")
        
        return failed == 0


def main():
    """Main test function"""
    print("🤖 Agentico Deployment Verification")
    print("Testing all services and endpoints...")
    print()
    
    # Wait a moment for services to be ready
    print("Waiting for services to be ready...")
    time.sleep(5)
    
    tester = DeploymentTester()
    tester.run_all_tests()
    success = tester.print_results()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
