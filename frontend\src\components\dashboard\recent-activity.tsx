'use client';

import { useQuery } from '@tanstack/react-query';
import {
  CpuChipIcon,
  ChatBubbleLeftRightIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  DocumentIcon,
  PaperAirplaneIcon,
} from '@heroicons/react/24/outline';
import { formatRelativeTime } from '@/lib/utils';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { dashboardService, Activity } from '@/services/dashboard-service';

// Icon mapping for different activity types
const getActivityIcon = (type: string) => {
  switch (type) {
    case 'agent_created':
      return { icon: CpuChipIcon, color: 'text-blue-500' };
    case 'conversation_started':
      return { icon: ChatBubbleLeftRightIcon, color: 'text-green-500' };
    case 'file_uploaded':
      return { icon: DocumentIcon, color: 'text-purple-500' };
    case 'message_sent':
      return { icon: PaperAirplaneIcon, color: 'text-indigo-500' };
    case 'task_completed':
      return { icon: CheckCircleIcon, color: 'text-green-500' };
    case 'task_failed':
      return { icon: ExclamationCircleIcon, color: 'text-red-500' };
    default:
      return { icon: CheckCircleIcon, color: 'text-gray-500' };
  }
};

export function RecentActivity() {
  const { data: activities = [], isLoading } = useQuery({
    queryKey: ['recent-activity'],
    queryFn: () => dashboardService.getRecentActivity(10),
  });

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">
        Recent Activity
      </h3>
      
      {isLoading ? (
        <div className="flex items-center justify-center h-32">
          <LoadingSpinner />
        </div>
      ) : (
        <div className="flow-root">
          <ul className="-mb-8">
            {activities.map((activity, activityIdx) => (
              <li key={activity.id}>
                <div className="relative pb-8">
                  {activityIdx !== activities.length - 1 ? (
                    <span
                      className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                      aria-hidden="true"
                    />
                  ) : null}
                  <div className="relative flex space-x-3">
                    <div>
                      {(() => {
                        const { icon: Icon, color } = getActivityIcon(activity.type);
                        return (
                          <span className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${
                            activity.status === 'success' ? 'bg-green-500' : 'bg-red-500'
                          }`}>
                            <Icon className={`h-5 w-5 text-white`} />
                          </span>
                        );
                      })()} 
                    </div>
                    <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                      <div>
                        <p className="text-sm text-gray-900 font-medium">
                          {activity.title}
                        </p>
                        <p className="text-sm text-gray-500">
                          {activity.description}
                        </p>
                      </div>
                      <div className="text-right text-sm whitespace-nowrap text-gray-500">
                        {formatRelativeTime(activity.timestamp)}
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
      
      {!isLoading && activities.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">No recent activity</p>
        </div>
      )}
    </div>
  );
}
