# Agentico Deployment Script for Windows PowerShell
# This script sets up and deploys the Agentico AI Agent Platform

# Set error action preference
$ErrorActionPreference = "Stop"

Write-Host "[INFO] Starting Agentico Deployment..." -ForegroundColor Green
Write-Host ""

# Function to check if a command exists
function Test-Command {
    param($Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# Function to test Docker connectivity
function Test-DockerRunning {
    try {
        docker info | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# Function to wait for backend health check
function Wait-BackendReady {
    $maxAttempts = 60
    $attempt = 0
    
    Write-Host "[INFO] Waiting for backend to be ready..." -ForegroundColor Yellow
    
    while ($attempt -lt $maxAttempts) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:8000/health" -Method GET -TimeoutSec 5 -ErrorAction Stop
            if ($response.StatusCode -eq 200) {
                Write-Host "[SUCCESS] Backend is ready!" -ForegroundColor Green
                return $true
            }
        }
        catch {
            # Backend not ready yet
        }
        
        Write-Host "Waiting for backend API... (attempt $($attempt + 1)/$maxAttempts)" -ForegroundColor Yellow
        Start-Sleep -Seconds 5
        $attempt++
    }
    
    Write-Host "[ERROR] Backend failed to start within expected time" -ForegroundColor Red
    return $false
}

# Check if Docker is installed
Write-Host "[INFO] Checking Docker installation..." -ForegroundColor Yellow

if (-not (Test-Command "docker")) {
    Write-Host "[ERROR] Docker is not installed or not in PATH." -ForegroundColor Red
    Write-Host "Please install Docker Desktop from: https://www.docker.com/products/docker-desktop/" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check Docker version
try {
    $dockerVersion = docker --version
    Write-Host "[SUCCESS] Docker found: $dockerVersion" -ForegroundColor Green
}
catch {
    Write-Host "[ERROR] Failed to get Docker version" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if Docker Desktop is running
Write-Host "[INFO] Checking if Docker Desktop is running..." -ForegroundColor Yellow

if (-not (Test-DockerRunning)) {
    Write-Host "[ERROR] Docker Desktop is not running!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please start Docker Desktop and wait for it to be ready, then run this script again." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Steps:" -ForegroundColor Yellow
    Write-Host "1. Open Docker Desktop from Start Menu" -ForegroundColor Yellow
    Write-Host "2. Wait for the Docker icon in system tray to show 'Docker Desktop is running'" -ForegroundColor Yellow
    Write-Host "3. Run this script again" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "[SUCCESS] Docker Desktop is running" -ForegroundColor Green
Write-Host ""

# Check if .env file exists
Write-Host "[INFO] Checking .env file..." -ForegroundColor Yellow

if (-not (Test-Path ".env")) {
    Write-Host "[WARNING] .env file not found. Creating from template..." -ForegroundColor Yellow
    Copy-Item ".env.example" ".env"
    Write-Host "[WARNING] Please edit .env file with your configuration before continuing." -ForegroundColor Yellow
    Write-Host "[WARNING] Especially set your API keys and change the default secrets!" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press any key to continue after editing .env file"
} else {
    Write-Host "[SUCCESS] .env file found" -ForegroundColor Green
}

Write-Host ""

# Create necessary directories
Write-Host "[INFO] Creating necessary directories..." -ForegroundColor Yellow

$directories = @(
    "backend\logs",
    "backend\uploads", 
    "backend\temp",
    "frontend\.next"
)

foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
}

Write-Host "[SUCCESS] Directories created" -ForegroundColor Green
Write-Host ""

# Stop any existing containers
Write-Host "[INFO] Stopping existing containers..." -ForegroundColor Yellow
try {
    docker compose down
}
catch {
    Write-Host "[INFO] No existing containers to stop" -ForegroundColor Yellow
}
Write-Host ""

# Build images
Write-Host "[INFO] Building Docker images..." -ForegroundColor Yellow
try {
    docker compose build
    if ($LASTEXITCODE -ne 0) {
        throw "Docker build failed"
    }
}
catch {
    Write-Host "[ERROR] Failed to build Docker images" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host ""

# Start services
Write-Host "[INFO] Starting services..." -ForegroundColor Yellow
try {
    docker compose up -d
    if ($LASTEXITCODE -ne 0) {
        throw "Docker compose up failed"
    }
}
catch {
    Write-Host "[ERROR] Failed to start services" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host ""

# Wait for services to be ready
Write-Host "[INFO] Waiting for services to be ready..." -ForegroundColor Yellow
Write-Host "This may take a few minutes..." -ForegroundColor Yellow
Write-Host ""

# Wait for backend to be ready
if (-not (Wait-BackendReady)) {
    Write-Host "[ERROR] Deployment failed - backend did not start properly" -ForegroundColor Red
    Write-Host "You can check logs with: docker compose logs backend" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "[SUCCESS] All services are ready!" -ForegroundColor Green
Write-Host ""

# Show deployment status
Write-Host "[SUCCESS] Agentico Deployment Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "[INFO] Service Status:" -ForegroundColor Cyan
docker compose ps
Write-Host ""
Write-Host "[INFO] Access URLs:" -ForegroundColor Cyan
Write-Host "  Frontend:      http://localhost:3001" -ForegroundColor White
Write-Host "  Backend API:   http://localhost:8000" -ForegroundColor White
Write-Host "  API Docs:      http://localhost:8000/docs" -ForegroundColor White
Write-Host "  File Storage:  Local file system (no web interface)" -ForegroundColor White
Write-Host ""
Write-Host "[INFO] Next Steps:" -ForegroundColor Cyan
Write-Host "  1. Visit http://localhost:3001 to access the platform" -ForegroundColor White
Write-Host "  2. Create your first user account" -ForegroundColor White
Write-Host "  3. Set up your AI API keys in the settings" -ForegroundColor White
Write-Host "  4. Create your first AI agent" -ForegroundColor White
Write-Host ""
Write-Host "[INFO] Management Commands:" -ForegroundColor Cyan
Write-Host "  View logs:    docker compose logs -f [service]" -ForegroundColor White
Write-Host "  Stop:         docker compose down" -ForegroundColor White
Write-Host "  Restart:      docker compose restart [service]" -ForegroundColor White
Write-Host "  Update:       git pull; docker compose build; docker compose up -d" -ForegroundColor White
Write-Host ""

# Open browser
Write-Host "Opening Agentico in your default browser..." -ForegroundColor Yellow
Start-Process "http://localhost:3001"

Write-Host ""
Read-Host "Press Enter to exit"